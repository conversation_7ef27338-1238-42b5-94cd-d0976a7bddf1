"""
Test flow-preserving chunking vs structure-aware chunking
"""

import json
import datetime
from pathlib import Path
from llama_index.core.schema import TextNode
from app.core.chunking.flow_preserving_chunker import create_flow_preserving_chunker
from app.core.chunking.structure_aware_chunker import create_structure_aware_chunker


def test_flow_vs_structure_chunking():
    """Compare flow-preserving vs structure-aware chunking"""
    
    print("🔄 FLOW-PRESERVING vs STRUCTURE-AWARE CHUNKING COMPARISON")
    print("=" * 70)
    
    # Read article.txt
    article_path = "app/core/chunking/article.txt"
    if not Path(article_path).exists():
        print(f"❌ Article file not found: {article_path}")
        return
    
    with open(article_path, 'r', encoding='utf-8') as f:
        article_text = f.read()
    
    print(f"📄 Article: {len(article_text)} characters, {len(article_text.split())} words")
    
    # Create both chunkers
    flow_chunker = create_flow_preserving_chunker(
        max_tokens=800,
        min_chunk_size=200,
        overlap_sentences=2
    )
    
    structure_chunker = create_structure_aware_chunker(
        max_tokens=800,
        min_chunk_size=200,
        preserve_structure=True
    )
    
    # Create test node
    test_node = TextNode(
        text=article_text,
        metadata={"source": "article.txt", "test_type": "comparison"}
    )
    
    # Test flow-preserving chunker
    print(f"\n🌊 FLOW-PRESERVING CHUNKER:")
    print("-" * 40)
    
    flow_chunks, flow_stats = flow_chunker(test_node)
    
    print(f"   Chunks created: {flow_stats['chunks_created']}")
    print(f"   Avg tokens per chunk: {flow_stats['avg_tokens_per_chunk']:.0f}")
    print(f"   Flow quality: {flow_stats['flow_quality']:.2f}")
    print(f"   Semantic boundaries: {flow_stats['semantic_boundaries']}")
    
    # Test structure-aware chunker
    print(f"\n🏗️  STRUCTURE-AWARE CHUNKER:")
    print("-" * 40)
    
    structure_chunks, structure_stats = structure_chunker(test_node)
    
    print(f"   Chunks created: {structure_stats['chunks_created']}")
    print(f"   Sections found: {structure_stats.get('sections_found', 0)}")
    print(f"   Avg tokens per chunk: {structure_stats['avg_tokens_per_chunk']:.0f}")
    print(f"   Section types: {len(set(structure_stats.get('section_types', [])))}")
    
    # Compare chunk examples
    print(f"\n📝 CHUNK COMPARISON EXAMPLES:")
    print("=" * 70)
    
    print(f"\n🌊 FLOW-PRESERVING CHUNKS (showing first 3):")
    for i, chunk in enumerate(flow_chunks[:3]):
        chunk_text = chunk.node.text
        flow_pos = chunk.node.metadata.get('flow_position', 'unknown')
        preview = chunk_text[:150].replace('\n', ' ')
        ends_well = chunk_text.rstrip().endswith(('।', '?', '!'))
        
        print(f"\\nChunk {i+1} [{flow_pos}] ({len(chunk_text)} chars) {'✅' if ends_well else '❌'}:")
        print(f"   {preview}...")
    
    print(f"\n🏗️  STRUCTURE-AWARE CHUNKS (showing first 3):")
    for i, chunk in enumerate(structure_chunks[:3]):
        chunk_text = chunk.node.text
        section_type = chunk.node.metadata.get('section_type', 'unknown')
        preview = chunk_text[:150].replace('\n', ' ')
        ends_well = chunk_text.rstrip().endswith(('।', '?', '!'))
        
        print(f"\\nChunk {i+1} [{section_type}] ({len(chunk_text)} chars) {'✅' if ends_well else '❌'}:")
        print(f"   {preview}...")
    
    # Save detailed comparison to JSON
    timestamp = datetime.datetime.now().isoformat()
    
    comparison_data = {
        "comparison_info": {
            "timestamp": timestamp,
            "source_file": "article.txt",
            "document_length": len(article_text),
            "test_type": "flow_vs_structure"
        },
        "flow_preserving": {
            "stats": flow_stats,
            "chunks": [
                {
                    "chunk_id": i,
                    "text": chunk.node.text,
                    "length": len(chunk.node.text),
                    "flow_position": chunk.node.metadata.get('flow_position'),
                    "ends_semantically": chunk.node.text.rstrip().endswith(('।', '?', '!'))
                }
                for i, chunk in enumerate(flow_chunks)
            ]
        },
        "structure_aware": {
            "stats": structure_stats,
            "chunks": [
                {
                    "chunk_id": i,
                    "text": chunk.node.text,
                    "length": len(chunk.node.text),
                    "section_type": chunk.node.metadata.get('section_type'),
                    "ends_semantically": chunk.node.text.rstrip().endswith(('।', '?', '!'))
                }
                for i, chunk in enumerate(structure_chunks)
            ]
        },
        "comparison_analysis": {
            "flow_chunks_count": len(flow_chunks),
            "structure_chunks_count": len(structure_chunks),
            "flow_avg_length": flow_stats['avg_tokens_per_chunk'],
            "structure_avg_length": structure_stats['avg_tokens_per_chunk'],
            "flow_semantic_pct": round(flow_stats['semantic_boundaries'] / len(flow_chunks) * 100, 1),
            "structure_semantic_pct": round(sum(1 for chunk in structure_chunks if chunk.node.text.rstrip().endswith(('।', '?', '!'))) / len(structure_chunks) * 100, 1),
            "recommendation": "flow_preserving" if len(flow_chunks) < len(structure_chunks) else "structure_aware"
        }
    }
    
    # Save comparison
    output_file = f"flow_vs_structure_comparison_{timestamp.replace(':', '-').replace('.', '-')}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_data, f, ensure_ascii=False, indent=2)
    
    print(f"\\n💾 Detailed comparison saved to: {output_file}")
    
    # Summary
    print(f"\\n📊 SUMMARY:")
    print(f"   Flow-preserving: {len(flow_chunks)} chunks, avg {flow_stats['avg_tokens_per_chunk']:.0f} tokens")
    print(f"   Structure-aware: {len(structure_chunks)} chunks, avg {structure_stats['avg_tokens_per_chunk']:.0f} tokens")
    print(f"   Flow quality score: {flow_stats['flow_quality']:.2f}")
    print(f"   Recommendation: {'Flow-preserving' if len(flow_chunks) < len(structure_chunks) else 'Structure-aware'} for better readability")
    
    return output_file, comparison_data


def test_flow_chunking_only():
    """Test only flow-preserving chunking and save results"""
    
    print("\\n\\n🌊 FLOW-PRESERVING CHUNKING TEST")
    print("=" * 50)
    
    # Read article.txt
    article_path = "app/core/chunking/article.txt"
    if not Path(article_path).exists():
        print(f"❌ Article file not found: {article_path}")
        return
    
    with open(article_path, 'r', encoding='utf-8') as f:
        article_text = f.read()
    
    print(f"📄 Processing article: {len(article_text)} characters")
    
    # Create flow chunker
    chunker = create_flow_preserving_chunker(
        max_tokens=800,
        min_chunk_size=200,
        overlap_sentences=2
    )
    
    # Create test node
    test_node = TextNode(
        text=article_text,
        metadata={"source": "article.txt", "chunking_type": "flow_preserving"}
    )
    
    # Process
    chunks, stats = chunker(test_node)
    
    print(f"✅ Processing complete!")
    print(f"   Chunks created: {stats['chunks_created']}")
    print(f"   Avg tokens per chunk: {stats['avg_tokens_per_chunk']:.0f}")
    print(f"   Flow quality: {stats['flow_quality']:.2f}")
    print(f"   Semantic boundaries: {stats['semantic_boundaries']}/{len(chunks)} ({stats['semantic_boundaries']/len(chunks)*100:.1f}%)")
    
    # Prepare JSON output
    timestamp = datetime.datetime.now().isoformat()
    
    json_output = {
        "test_info": {
            "timestamp": timestamp,
            "source_file": "article.txt",
            "chunking_method": "flow_preserving",
            "chunker_config": {
                "max_tokens": 800,
                "min_chunk_size": 200,
                "overlap_sentences": 2
            }
        },
        "document_info": {
            "original_length": len(article_text),
            "original_words": len(article_text.split()),
            "preview": article_text[:300] + "..." if len(article_text) > 300 else article_text
        },
        "chunking_results": {
            "stats": stats,
            "chunks": [
                {
                    "chunk_id": i + 1,
                    "flow_position": chunk.node.metadata.get('flow_position'),
                    "content": {
                        "text": chunk.node.text,
                        "length": len(chunk.node.text),
                        "words": len(chunk.node.text.split()),
                        "preview": chunk.node.text[:100] + "..." if len(chunk.node.text) > 100 else chunk.node.text
                    },
                    "quality": {
                        "ends_semantically": chunk.node.text.rstrip().endswith(('।', '?', '!')),
                        "has_overlap": i > 0,  # All chunks except first have overlap
                        "length_appropriate": 200 <= len(chunk.node.text) <= 1000
                    }
                }
                for i, chunk in enumerate(chunks)
            ]
        }
    }
    
    # Save to JSON
    output_file = f"flow_preserving_chunking_{timestamp.replace(':', '-').replace('.', '-')}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_output, f, ensure_ascii=False, indent=2)
    
    print(f"💾 Results saved to: {output_file}")
    
    # Show chunk previews
    print(f"\\n📝 CHUNK PREVIEWS:")
    for i, chunk_info in enumerate(json_output["chunking_results"]["chunks"][:5]):
        flow_pos = chunk_info['flow_position']
        length = chunk_info['content']['length']
        preview = chunk_info['content']['preview']
        quality = "✅" if chunk_info['quality']['ends_semantically'] else "❌"
        
        print(f"   {i+1}. [{flow_pos}] ({length} chars) {quality}")
        print(f"      {preview}")
    
    if len(chunks) > 5:
        print(f"   ... and {len(chunks) - 5} more chunks")
    
    return output_file, json_output


if __name__ == "__main__":
    print("🧪 FLOW-PRESERVING CHUNKING TESTS")
    print("=" * 60)
    
    try:
        # Test flow-preserving only
        flow_file, flow_results = test_flow_chunking_only()
        
        # Compare with structure-aware
        comparison_file, comparison_results = test_flow_vs_structure_chunking()
        
        print(f"\\n✅ All tests completed!")
        print(f"📁 Flow-preserving results: {flow_file}")
        print(f"📁 Comparison results: {comparison_file}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
