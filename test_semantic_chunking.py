"""
Test the improved semantic chunking with smart ! handling
"""

from llama_index.core.schema import TextNode
from app.core.chunking import create_nepali_chunker


def test_exclamation_handling():
    """Test smart exclamation mark handling"""
    
    print("🔍 TESTING SMART EXCLAMATION HANDLING")
    print("=" * 50)
    
    # Test cases for exclamation handling
    test_cases = [
        {
            "name": "Greeting with ! (should NOT split)",
            "text": "माननीय न्यायाधीश! यो मुद्दामा निम्न तथ्यहरू छन्। पहिलो तथ्य यो हो कि वादीले दाबी गरेको छ।"
        },
        {
            "name": "Sentence ending with ! (should split)",
            "text": "यो निर्णय अन्तिम र बाध्यकारी छ! अब अर्को मुद्दामा जानुपर्छ। यसमा कुनै शंका छैन।"
        },
        {
            "name": "Mixed punctuation",
            "text": "सम्माननीय अदालत! यो मुद्दा धेरै जटिल छ। के यो सत्य हो? हो, यो सत्य हो। अन्तमा निर्णय गर्नुपर्छ।"
        }
    ]
    
    chunker = create_nepali_chunker(max_tokens=200, overlap=1, min_chunk_size=30)
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📝 Test {i+1}: {test_case['name']}")
        print(f"Text: {test_case['text']}")
        
        # Test sentence splitting
        sentences = chunker.split_sentences(test_case['text'])
        print(f"Sentences found: {len(sentences)}")
        for j, sentence in enumerate(sentences):
            print(f"  {j+1}: {sentence}")


def test_semantic_chunking():
    """Test semantic chunking vs token-based chunking"""
    
    print("\n\n🧠 TESTING SEMANTIC CHUNKING")
    print("=" * 50)
    
    # Legal document with clear semantic boundaries
    legal_text = """निर्णय नं. १०१६४ - निर्णय दर्ता बदर

सर्वोच्च अदालत, संयुक्त इजलास

सम्माननीय प्रधानन्यायाधीश श्री गोपाल पराजुली! माननीय न्यायाधीश श्री केदारप्रसाद चालिसे!

मुद्दा:- निर्णय दर्ता बदर

पुनरावेदक / प्रतिवादी : काठमाडौं जिल्ला, कपन गा.वि.स. वडा नं. ३ बस्ने कान्छी तामाङसमेत

विरूद्ध

प्रत्यर्थी / वादी : का.जि.का.म.न.पा. वडा नं. २३ बस्ने नातीभाइ गुभाजु भन्ने नाति शाक्यकी श्रीमती रत्नमाया शाक्य

विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने। यो एक महत्वपूर्ण कानुनी सिद्धान्त हो।

कुनै मुद्दाको रोहमा प्रमाण बुझ्ने प्रसङ्गमा जारी भएको म्याद पक्षले प्राप्त गर्दैमा सो मुद्दामा पछि हुने निर्णयसमेत निजलाई जानकारी थियो भन्न नमिल्ने। यो अर्को महत्वपूर्ण बिन्दु हो।

प्रकरण नं. २

न्यायालयको यो निर्णय अन्तिम र बाध्यकारी छ! सबै पक्षहरूले यसलाई मान्नुपर्छ।"""

    # Test with different chunk sizes
    chunk_sizes = [300, 500, 800]
    
    for chunk_size in chunk_sizes:
        print(f"\n📊 Testing with max_tokens={chunk_size}")
        print("-" * 30)
        
        chunker = create_nepali_chunker(
            max_tokens=chunk_size, 
            overlap=1, 
            min_chunk_size=100
        )
        
        test_node = TextNode(text=legal_text, metadata={"test": True})
        chunks, stats = chunker(test_node)
        
        print(f"Statistics: {stats}")
        print(f"Semantic quality: {stats.get('semantic_quality', 0):.2f} (1.0 = perfect)")
        
        print(f"\nChunks created: {len(chunks)}")
        for i, chunk in enumerate(chunks):
            chunk_text = chunk.node.text
            ends_semantically = chunk_text.rstrip().endswith(('।', '?', '!'))
            print(f"\nChunk {i+1} ({'✅' if ends_semantically else '❌'} semantic ending):")
            print(f"  Length: {len(chunk_text)} chars")
            print(f"  Text: {chunk_text[:150]}...")
            print(f"  Ends with: '{chunk_text.rstrip()[-3:]}'")


def test_comparison_with_old_method():
    """Compare with simple token-based splitting"""
    
    print("\n\n⚖️  COMPARISON: SEMANTIC vs SIMPLE SPLITTING")
    print("=" * 60)
    
    test_text = """माननीय न्यायाधीश! यो मुद्दा जटिल छ। पहिलो तथ्य यो हो कि वादीले जग्गाको हक दाबी गरेको छ। दोस्रो तथ्य यो हो कि प्रतिवादीले त्यो जग्गा आफ्नो भएको भनेको छ। के यो सत्य हो? यो प्रश्न महत्वपूर्ण छ। अदालतले यसको जवाफ दिनुपर्छ।

प्रकरण नं. १

न्यायालयको विचारमा यो मुद्दा स्पष्ट छ। प्रमाणहरू हेर्दा वादीको दाबी सही देखिन्छ। तसर्थ यो निर्णय गरिन्छ।"""

    print("🔴 OLD METHOD (Simple regex split):")
    # Simulate old method
    import re
    old_sentences = re.split(r'[।!?]', test_text)
    old_sentences = [s.strip() for s in old_sentences if s.strip() and len(s.strip()) > 10]
    
    print(f"Sentences: {len(old_sentences)}")
    for i, sentence in enumerate(old_sentences[:3]):
        print(f"  {i+1}: {sentence[:100]}...")
    
    print(f"\n🟢 NEW METHOD (Semantic with smart ! handling):")
    chunker = create_nepali_chunker(max_tokens=400, overlap=1)
    new_sentences = chunker.split_sentences(test_text)
    
    print(f"Sentences: {len(new_sentences)}")
    for i, sentence in enumerate(new_sentences[:3]):
        print(f"  {i+1}: {sentence[:100]}...")
    
    # Test chunking
    test_node = TextNode(text=test_text, metadata={"test": True})
    chunks, stats = chunker(test_node)
    
    print(f"\nChunking results:")
    print(f"  Chunks: {stats['chunks_created']}")
    print(f"  Semantic quality: {stats.get('semantic_quality', 0):.2f}")
    print(f"  Avg tokens per chunk: {stats['avg_tokens_per_chunk']:.0f}")


if __name__ == "__main__":
    print("🚀 SEMANTIC NEPALI CHUNKING TESTS")
    print("=" * 60)
    
    # Test exclamation handling
    test_exclamation_handling()
    
    # Test semantic chunking
    test_semantic_chunking()
    
    # Compare with old method
    test_comparison_with_old_method()
    
    print("\n✅ All tests completed!")
    print("\n📋 KEY IMPROVEMENTS:")
    print("1. Smart ! handling - only splits after 3+ words (not greetings)")
    print("2. Semantic boundaries - chunks end at complete thoughts")
    print("3. Context awareness - preserves legal document structure")
    print("4. Quality metrics - tracks semantic completeness")
