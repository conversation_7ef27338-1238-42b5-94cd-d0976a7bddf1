"""
Test flow-preserving chunking with line tracking
"""

import json
import datetime
from pathlib import Path
from llama_index.core.schema import TextNode
from app.core.chunking.flow_preserving_chunker import create_flow_preserving_chunker


def test_line_tracking_chunking():
    """Test chunking with line number tracking"""
    
    print("📍 FLOW-PRESERVING CHUNKING WITH LINE TRACKING")
    print("=" * 60)
    
    # Read article.txt
    article_path = "app/core/chunking/article.txt"
    if not Path(article_path).exists():
        print(f"❌ Article file not found: {article_path}")
        return
    
    with open(article_path, 'r', encoding='utf-8') as f:
        article_text = f.read()
    
    # Count original lines
    original_lines = article_text.split('\n')
    print(f"📄 Article: {len(article_text)} characters, {len(original_lines)} lines")
    
    # Create flow chunker
    chunker = create_flow_preserving_chunker(
        max_tokens=800,
        min_chunk_size=200,
        overlap_sentences=2
    )
    
    # Create test node
    test_node = TextNode(
        text=article_text,
        metadata={"source": "article.txt", "test_type": "line_tracking"}
    )
    
    # Process
    chunks, stats = chunker(test_node)
    
    print(f"✅ Processing complete!")
    print(f"   Total lines in document: {stats.get('total_lines', 0)}")
    print(f"   Line coverage: {stats.get('line_coverage', 'unknown')}")
    print(f"   Chunks created: {stats['chunks_created']}")
    print(f"   Avg tokens per chunk: {stats['avg_tokens_per_chunk']:.0f}")
    print(f"   Flow quality: {stats['flow_quality']:.2f}")
    
    # Prepare JSON output with line tracking
    timestamp = datetime.datetime.now().isoformat()
    
    json_output = {
        "test_info": {
            "timestamp": timestamp,
            "source_file": "article.txt",
            "chunking_method": "flow_preserving_with_line_tracking",
            "chunker_config": {
                "max_tokens": 800,
                "min_chunk_size": 200,
                "overlap_sentences": 2
            }
        },
        "document_info": {
            "original_length": len(article_text),
            "original_lines": len(original_lines),
            "original_words": len(article_text.split()),
            "line_coverage": stats.get('line_coverage', 'unknown')
        },
        "chunking_results": {
            "stats": stats,
            "chunks": []
        }
    }
    
    # Process each chunk with detailed line information
    print(f"\n📝 CHUNKS WITH LINE TRACKING:")
    print("-" * 60)
    
    for i, chunk in enumerate(chunks):
        chunk_text = chunk.node.text
        chunk_metadata = chunk.node.metadata
        
        start_line = chunk_metadata.get('start_line', 0)
        end_line = chunk_metadata.get('end_line', 0)
        line_span = chunk_metadata.get('line_span', 0)
        flow_position = chunk_metadata.get('flow_position', 'unknown')
        
        # Show original lines for this chunk
        chunk_original_lines = original_lines[start_line-1:end_line] if start_line > 0 else []
        
        chunk_info = {
            "chunk_id": i + 1,
            "flow_position": flow_position,
            "line_info": {
                "start_line": start_line,
                "end_line": end_line,
                "line_span": line_span,
                "original_lines": chunk_original_lines
            },
            "content": {
                "text": chunk_text,
                "length": len(chunk_text),
                "words": len(chunk_text.split()),
                "preview": chunk_text[:150] + "..." if len(chunk_text) > 150 else chunk_text
            },
            "quality": {
                "ends_semantically": chunk_text.rstrip().endswith(('।', '?', '!')),
                "has_overlap": i > 0,
                "length_appropriate": 200 <= len(chunk_text) <= 1000
            }
        }
        
        json_output["chunking_results"]["chunks"].append(chunk_info)
        
        # Display chunk info
        quality_icon = "✅" if chunk_info['quality']['ends_semantically'] else "❌"
        print(f"\nChunk {i+1} [{flow_position}] {quality_icon}")
        print(f"   Lines: {start_line}-{end_line} (span: {line_span} lines)")
        print(f"   Length: {len(chunk_text)} chars, {len(chunk_text.split())} words")
        print(f"   Preview: {chunk_text[:100].replace(chr(10), ' ')}...")
        
        # Show first few original lines
        if chunk_original_lines:
            print(f"   Original lines:")
            for j, line in enumerate(chunk_original_lines[:3]):
                line_num = start_line + j
                print(f"     {line_num:3d}: {line[:60]}{'...' if len(line) > 60 else ''}")
            if len(chunk_original_lines) > 3:
                print(f"     ... and {len(chunk_original_lines) - 3} more lines")
    
    # Save to JSON
    output_file = f"line_tracking_chunking_{timestamp.replace(':', '-').replace('.', '-')}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_output, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    # Summary with line analysis
    print(f"\n📊 LINE TRACKING SUMMARY:")
    print(f"   Document lines: {len(original_lines)}")
    print(f"   Lines covered: {stats.get('line_coverage', 'unknown')}")
    print(f"   Chunks created: {len(chunks)}")
    print(f"   Avg lines per chunk: {len(original_lines) / len(chunks):.1f}")
    
    # Show line distribution
    line_spans = [chunk.node.metadata.get('line_span', 0) for chunk in chunks]
    print(f"   Line span range: {min(line_spans)}-{max(line_spans)} lines")
    print(f"   Avg line span: {sum(line_spans) / len(line_spans):.1f} lines")
    
    return output_file, json_output


def show_line_mapping_example():
    """Show a simple example of line tracking"""
    
    print("\n\n🔍 LINE MAPPING EXAMPLE")
    print("=" * 40)
    
    sample_text = """निर्णय नं. १०१६४ - निर्णय दर्ता बदर

भाग: ६१ साल: २०७६ महिना: बैशाख अंक: १

फैसला मिति :२०७४/०९/०३ २३९९

सर्वोच्च अदालत, संयुक्त इजलास

मुद्दा:- निर्णय दर्ता बदर

पुनरावेदक / प्रतिवादी : काठमाडौं जिल्ला बस्ने व्यक्ति

विरूद्ध

प्रत्यर्थी / वादी : काठमाडौं बस्ने अर्को व्यक्ति

विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने।

कुनै मुद्दाको रोहमा प्रमाण बुझ्ने प्रसङ्गमा जारी भएको म्याद पक्षले प्राप्त गर्दैमा सो मुद्दामा पछि हुने निर्णयसमेत निजलाई जानकारी थियो भन्न नमिल्ने।"""

    print("Sample text with line numbers:")
    lines = sample_text.split('\n')
    for i, line in enumerate(lines, 1):
        print(f"{i:2d}: {line}")
    
    # Test chunking
    chunker = create_flow_preserving_chunker(max_tokens=300, min_chunk_size=100)
    test_node = TextNode(text=sample_text, metadata={"test": True})
    
    chunks, stats = chunker(test_node)
    
    print(f"\nChunking results:")
    for i, chunk in enumerate(chunks):
        metadata = chunk.node.metadata
        start_line = metadata.get('start_line', 0)
        end_line = metadata.get('end_line', 0)
        
        print(f"\nChunk {i+1}: Lines {start_line}-{end_line}")
        print(f"Text: {chunk.node.text[:100]}...")


if __name__ == "__main__":
    print("🧪 LINE TRACKING CHUNKING TEST")
    print("=" * 50)
    
    try:
        # Test with article file
        output_file, results = test_line_tracking_chunking()
        
        # Show simple example
        show_line_mapping_example()
        
        print(f"\n✅ All tests completed!")
        print(f"📁 Detailed results: {output_file}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
