"""
Test the structure-aware chunking on the actual article.txt file
"""

from llama_index.core.schema import TextNode
from app.core.chunking.structure_aware_chunker import create_structure_aware_chunker


def test_structure_chunking():
    """Test structure-aware chunking on the article.txt file"""
    
    print("🏗️  STRUCTURE-AWARE CHUNKING TEST")
    print("=" * 60)
    
    # Read the article.txt file
    try:
        with open("app/core/chunking/article.txt", "r", encoding="utf-8") as f:
            article_text = f.read()
    except FileNotFoundError:
        print("❌ article.txt file not found!")
        return
    
    print(f"📄 Original document length: {len(article_text)} characters")
    print(f"📄 Original document lines: {len(article_text.split('\\n'))} lines")
    
    # Create structure-aware chunker
    chunker = create_structure_aware_chunker(
        max_tokens=600,  # Smaller chunks for better structure
        min_chunk_size=100,
        preserve_structure=True
    )
    
    # Create test node
    test_node = TextNode(
        text=article_text,
        metadata={"document_type": "legal_decision", "source": "article.txt"}
    )
    
    # Process with structure-aware chunker
    chunks, stats = chunker(test_node)
    
    print(f"\\n📊 CHUNKING STATISTICS:")
    print(f"  Total sections found: {stats['sections_found']}")
    print(f"  Section types: {stats['section_types']}")
    print(f"  Chunks created: {stats['chunks_created']}")
    print(f"  Avg tokens per chunk: {stats['avg_tokens_per_chunk']:.0f}")
    
    print(f"\\n📝 CHUNK BREAKDOWN:")
    print("-" * 60)
    
    for i, chunk in enumerate(chunks):
        chunk_text = chunk.node.text
        metadata = chunk.node.metadata
        
        print(f"\\n🔹 Chunk {i+1}: {metadata['section_type'].upper()}")
        print(f"   Title: {metadata.get('section_title', 'N/A')}")
        print(f"   Length: {len(chunk_text)} chars")
        print(f"   Sections: {metadata.get('sections', 1)}")
        
        # Show first few lines
        lines = chunk_text.split('\\n')[:3]
        for j, line in enumerate(lines):
            if line.strip():
                print(f"   Line {j+1}: {line.strip()[:80]}...")
        
        # Show if it ends properly
        ends_well = chunk_text.rstrip().endswith(('।', '?', '!', '।'))
        print(f"   Ends semantically: {'✅' if ends_well else '❌'}")
    
    return chunks, stats


def analyze_document_structure():
    """Analyze the structure patterns in the document"""
    
    print("\\n\\n🔍 DOCUMENT STRUCTURE ANALYSIS")
    print("=" * 60)
    
    try:
        with open("app/core/chunking/article.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
    except FileNotFoundError:
        print("❌ article.txt file not found!")
        return
    
    print(f"📄 Total lines: {len(lines)}")
    
    # Analyze patterns
    patterns = {
        "header_patterns": [],
        "case_info_patterns": [],
        "content_patterns": [],
        "decision_patterns": [],
        "footer_patterns": [],
        "nbsp_locations": []
    }
    
    for i, line in enumerate(lines):
        line_clean = line.strip()
        
        if not line_clean:
            continue
        
        if line_clean == '&nbsp;':
            patterns["nbsp_locations"].append(i+1)
            continue
        
        # Header patterns
        if i < 20:
            if any(pattern in line_clean for pattern in ['निर्णय नं', 'भाग:', 'फैसला मिति', 'सर्वोच्च', 'माननीय', 'सम्माननीय']):
                patterns["header_patterns"].append(f"Line {i+1}: {line_clean[:50]}...")
        
        # Case info patterns
        if any(pattern in line_clean for pattern in ['मुद्दा:-', 'पुनरावेदक', 'प्रत्यर्थी', 'विरूद्ध']):
            patterns["case_info_patterns"].append(f"Line {i+1}: {line_clean[:50]}...")
        
        # Content patterns
        if 'प्रकरण नं' in line_clean:
            patterns["content_patterns"].append(f"Line {i+1}: {line_clean}")
        
        # Decision patterns
        if line_clean == 'फैसला':
            patterns["decision_patterns"].append(f"Line {i+1}: {line_clean}")
        
        # Footer patterns
        if any(pattern in line_clean for pattern in ['उक्त रायमा सहमत', 'न्या.', 'इजलास', 'इति संवत्', 'भर्खरै प्रकाशित', 'धेरै हेरिएका']):
            patterns["footer_patterns"].append(f"Line {i+1}: {line_clean[:50]}...")
    
    # Print analysis
    for pattern_type, pattern_list in patterns.items():
        if pattern_list:
            print(f"\\n🔸 {pattern_type.replace('_', ' ').title()}:")
            for pattern in pattern_list[:5]:  # Show first 5
                print(f"   {pattern}")
            if len(pattern_list) > 5:
                print(f"   ... and {len(pattern_list) - 5} more")


def compare_chunking_approaches():
    """Compare structure-aware vs simple chunking"""
    
    print("\\n\\n⚖️  CHUNKING APPROACH COMPARISON")
    print("=" * 60)
    
    try:
        with open("app/core/chunking/article.txt", "r", encoding="utf-8") as f:
            article_text = f.read()
    except FileNotFoundError:
        print("❌ article.txt file not found!")
        return
    
    test_node = TextNode(text=article_text, metadata={"test": True})
    
    # 1. Structure-aware chunking
    print("\\n🏗️  STRUCTURE-AWARE CHUNKING:")
    structure_chunker = create_structure_aware_chunker(max_tokens=600)
    structure_chunks, structure_stats = structure_chunker(test_node)
    
    print(f"   Chunks: {structure_stats['chunks_created']}")
    print(f"   Sections: {structure_stats['sections_found']}")
    print(f"   Avg tokens: {structure_stats['avg_tokens_per_chunk']:.0f}")
    
    # Show section types
    section_types = {}
    for chunk in structure_chunks:
        section_type = chunk.node.metadata['section_type']
        section_types[section_type] = section_types.get(section_type, 0) + 1
    
    print(f"   Section distribution: {section_types}")
    
    # 2. Simple token-based chunking (simulate)
    print("\\n📏 SIMPLE TOKEN-BASED CHUNKING (simulated):")
    
    # Simple approach: split by tokens
    words = article_text.split()
    simple_chunks = []
    current_chunk = []
    current_tokens = 0
    
    for word in words:
        current_tokens += len(word)
        current_chunk.append(word)
        
        if current_tokens >= 600:
            simple_chunks.append(' '.join(current_chunk))
            current_chunk = []
            current_tokens = 0
    
    if current_chunk:
        simple_chunks.append(' '.join(current_chunk))
    
    print(f"   Chunks: {len(simple_chunks)}")
    print(f"   Avg tokens: {sum(len(c) for c in simple_chunks) / len(simple_chunks):.0f}")
    
    # Check semantic endings
    semantic_endings = sum(1 for chunk in simple_chunks if chunk.rstrip().endswith(('।', '?', '!')))
    print(f"   Semantic endings: {semantic_endings}/{len(simple_chunks)} ({semantic_endings/len(simple_chunks)*100:.1f}%)")
    
    # Structure-aware semantic endings
    structure_semantic = sum(1 for chunk in structure_chunks if chunk.node.text.rstrip().endswith(('।', '?', '!')))
    print(f"   Structure-aware semantic endings: {structure_semantic}/{len(structure_chunks)} ({structure_semantic/len(structure_chunks)*100:.1f}%)")


if __name__ == "__main__":
    print("🚀 NEPALI LEGAL DOCUMENT STRUCTURE ANALYSIS")
    print("=" * 70)
    
    # Analyze document structure
    analyze_document_structure()
    
    # Test structure-aware chunking
    test_structure_chunking()
    
    # Compare approaches
    compare_chunking_approaches()
    
    print("\\n✅ Analysis completed!")
    print("\\n📋 KEY FINDINGS:")
    print("1. Document has clear structural sections (header, case info, decision, footer)")
    print("2. &nbsp; markers separate major sections")
    print("3. प्रकरण नं.X marks subsections within content")
    print("4. Structure-aware chunking preserves semantic boundaries")
    print("5. Each chunk represents a complete thought/section")
