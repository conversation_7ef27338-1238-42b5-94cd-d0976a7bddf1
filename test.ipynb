from pymongo import AsyncMongoClient
import os
uri=os.getenv("MONGO_URI")
client = AsyncMongoClient(uri)
db = client["legal_backend_db"]
collection = db["documents"]


doc=await collection.find().to_list()
for d in doc:
    d["metadata"]["year"]=d["year"]
    d["metadata"]["document_id"]=str(d["_id"])

from llama_index.core.schema import TextNode
llama_docs=[TextNode(text=d["text"],metadata=d["metadata"]) for d in doc]

llama_docs

"""
Improved Nepali Text Chunking for Legal Documents

This module provides better sentence splitting and chunking for Nepali legal documents
by understanding the structure and patterns specific to legal text.
"""

import re
from typing import List, Dict, Any
from pydantic import Field, BaseModel
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode


class ImprovedNepaliSentenceChunkTransform(TransformComponent):
    """
    Enhanced Nepali sentence chunker specifically designed for legal documents.
    
    Features:
    - Better sentence boundary detection for Nepali text
    - Legal document structure awareness
    - Noise filtering (page numbers, headers, footers)
    - Contextual chunking with proper overlap
    - Metadata preservation
    """
    
    max_tokens: int = Field(default=500, description="Maximum tokens per chunk")
    overlap: int = Field(default=2, description="Number of sentences to overlap between chunks")
    min_chunk_size: int = Field(default=100, description="Minimum tokens per chunk")
    preserve_structure: bool = Field(default=True, description="Preserve document structure markers")

    def split_sentences(self, text: str) -> List[str]:
        """
        Improved Nepali sentence splitting for legal documents.
        
        Handles:
        - Nepali punctuation (।, !, ?)
        - Date patterns (२०७४/०९/०३)
        - Legal section markers (नं. १०१६४)
        - Court case patterns
        - Paragraph boundaries
        """
        # Clean the text first
        text = self.clean_text(text)
        
        if not text.strip():
            return []
        
        # Split by major structural boundaries first
        major_sections = self.split_by_structure(text)
        
        sentences = []
        for section in major_sections:
            section_sentences = self.split_section_sentences(section)
            sentences.extend(section_sentences)
        
        # Filter and clean sentences
        filtered_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if self.is_valid_sentence(sentence):
                filtered_sentences.append(sentence)
        
        return filtered_sentences
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize the text"""
        # Remove HTML entities and tags
        text = re.sub(r'&nbsp;|&amp;|&lt;|&gt;|&quot;', ' ', text)
        text = re.sub(r'<[^>]+>', '', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # Clean up multiple punctuation
        text = re.sub(r'[।]{2,}', '।', text)
        text = re.sub(r'[\.]{3,}', '...', text)
        
        # Remove page markers and navigation elements
        text = re.sub(r'\d+\s*-->', '', text)
        text = re.sub(r'५२२-->', '', text)
        
        return text.strip()
    
    def split_by_structure(self, text: str) -> List[str]:
        """Split text by major structural elements"""
        # Patterns for major sections
        structure_patterns = [
            # Decision headers
            r'(?=निर्णय नं\. \d+)',
            # Court information
            r'(?=सर्वोच्च अदालत)',
            # Case details
            r'(?=मुद्दा[ः:])',
            # Parties section
            r'(?=पुनरावेदक / प्रतिवादी)',
            # Decision section
            r'(?=फैसला\s*\n)',
            # Major paragraph breaks with substantial content
            r'\n\s*(?=\d+\.\s+[^\n]{50,})',
        ]
        
        sections = [text]
        for pattern in structure_patterns:
            new_sections = []
            for section in sections:
                splits = re.split(pattern, section)
                new_sections.extend([s.strip() for s in splits if s.strip()])
            sections = new_sections
        
        return sections
    
    def split_section_sentences(self, text: str) -> List[str]:
        """Split a section into sentences"""
        # Much simpler approach to avoid regex complexity
        sentences = []

        # First split by major punctuation
        parts = re.split(r'[।!?]', text)

        for part in parts:
            part = part.strip()
            if not part:
                continue

            # Further split by line breaks if the part is long
            if len(part) > 200:
                sub_parts = part.split('\n')
                for sub_part in sub_parts:
                    sub_part = sub_part.strip()
                    if len(sub_part) > 20:
                        sentences.append(sub_part)
            elif len(part) > 20:
                sentences.append(part)

        return sentences
    
    def is_valid_sentence(self, sentence: str) -> bool:
        """Check if sentence is valid and not noise"""
        sentence = sentence.strip()
        
        # Too short
        if len(sentence) < 15:
            return False
        
        # Check for noise patterns
        if self.is_noise_sentence(sentence):
            return False
        
        # Must contain some meaningful content (not just punctuation/numbers)
        meaningful_chars = re.sub(r'[\s\d\.\-।,;:()]+', '', sentence)
        if len(meaningful_chars) < 10:
            return False
        
        return True
    
    def is_noise_sentence(self, sentence: str) -> bool:
        """Check if sentence is likely noise/metadata"""
        sentence = sentence.strip()
        
        noise_patterns = [
            r'^\d+$',  # Just numbers
            r'^[।\.\-\s,;:()]+$',  # Just punctuation
            r'^भर्खरै प्रकाशित नजिरहरू',  # Recently published
            r'^धेरै हेरिएका नजिरहरु',  # Most viewed
            r'^निर्णय नं:\s*#\s*\d+',  # Decision number headers
            r'^फैसला मिति\s*:\s*\d+',  # Decision date headers
            r'^\d+\s*-->',  # Page markers
            r'^इति संवत्',  # Date endings
            r'^उक्त रायमा सहमत छु',  # Agreement statements
            r'^इजलास अधिकृत',  # Court officer
            r'^न्या\.',  # Judge abbreviation
            r'^मा\.',  # Honorable abbreviation
            r'^श्री',  # Title only
            r'^\d+\s*\|\s*मुद्दा नं',  # Case number format
        ]
        
        for pattern in noise_patterns:
            if re.match(pattern, sentence):
                return True
        
        return False
    
    def create_chunks_with_overlap(self, sentences: List[str]) -> List[str]:
        """Create chunks with proper overlap"""
        if not sentences:
            return []
        
        chunks = []
        current_chunk = []
        current_tokens = 0
        
        i = 0
        while i < len(sentences):
            sentence = sentences[i]
            sentence_tokens = len(sentence)
            
            # If adding this sentence would exceed max_tokens and we have content
            if current_tokens + sentence_tokens > self.max_tokens and current_chunk:
                # Create chunk from current sentences
                chunk_text = ' '.join(current_chunk)
                if len(chunk_text.strip()) >= self.min_chunk_size:
                    chunks.append(chunk_text)
                
                # Start new chunk with overlap
                if self.overlap > 0 and len(current_chunk) > self.overlap:
                    current_chunk = current_chunk[-self.overlap:]
                    current_tokens = sum(len(s) for s in current_chunk)
                else:
                    current_chunk = []
                    current_tokens = 0
            else:
                # Add sentence to current chunk
                current_chunk.append(sentence)
                current_tokens += sentence_tokens
                i += 1
        
        # Add final chunk if it has content
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            if len(chunk_text.strip()) >= self.min_chunk_size:
                chunks.append(chunk_text)
        
        return chunks
    
    def transform_single_text_node(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Transform a single text node into chunks"""
        sentences = self.split_sentences(node.text)
        
        if not sentences:
            return [], {"error": "No valid sentences found"}
        
        chunks = self.create_chunks_with_overlap(sentences)
        
        new_nodes = []
        for i, chunk_text in enumerate(chunks):
            # Preserve original metadata and add chunk info
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_tokens": len(chunk_text),
                "source_node_id": node.node_id if hasattr(node, 'node_id') else None
            })
            
            chunk_node = TextNode(
                text=chunk_text,
                metadata=chunk_metadata
            )
            new_nodes.append(NodeWithScore(node=chunk_node, score=None))
        
        stats = {
            "total_tokens": len(node.text),
            "total_sentences": len(sentences),
            "chunks_created": len(chunks),
            "avg_sentences_per_chunk": round(len(sentences) / len(chunks), 2) if chunks else 0,
            "avg_tokens_per_chunk": round(sum(len(c) for c in chunks) / len(chunks), 2) if chunks else 0
        }
        
        return new_nodes, stats
    
    def __call__(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Main entry point for transformation"""
        return self.transform_single_text_node(node)
# Create transformer
transformer = ImprovedNepaliSentenceChunkTransform(
        max_tokens=600,
        overlap=1,
        min_chunk_size=50
    )

# Test with first document (single TextNode, not list)
chunks, stats = transformer(llama_docs[0])

print(f"📊 CHUNKING RESULTS:")
print(f"Original text length: {stats['total_tokens']} tokens")
print(f"Sentences found: {stats['total_sentences']}")
print(f"Chunks created: {stats['chunks_created']}")
print(f"Avg sentences per chunk: {stats['avg_sentences_per_chunk']}")
print(f"Avg tokens per chunk: {stats['avg_tokens_per_chunk']}")

print(f"\n📝 SAMPLE CHUNKS:")
for i, chunk in enumerate(chunks):  # Show first 2 chunks
    print(f"\nChunk {i+1}:")
    print(chunk.node.text)
    print(f"Metadata: {chunk.node.metadata}")

