from pymongo import AsyncMongoClient
import os
uri=os.getenv("MONGO_URI")
client = AsyncMongoClient(uri)
db = client["legal_backend_db"]
collection = db["documents"]


doc=await collection.find().to_list()

from llama_index.core.schema import TextNode
llama_docs=[TextNode(text=d["text"],metadata=d["metadata"]) for d in doc]

llama_docs

import re
from pydantic import Field
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode

class NepaliSentenceChunkTransform(TransformComponent):
    max_tokens: int = Field(default=500, description="Maximum tokens per chunk")
    overlap: int = Field(default=0, description="Number of sentences to overlap between chunks")

    def split_sentences(self, text: str) -> list[str]:
        return re.split(r'(?<=[।!?])\s+', text)

    def transform_single_text_node(self, node: TextNode) -> tuple[list[NodeWithScore], dict]:
        sentences = self.split_sentences(node.text)
        total_sentences = len(sentences)
        total_tokens = len(node.text)

        new_nodes = []
        chunk = []
        token_count = 0

        for i, sentence in enumerate(sentences):
            token_count += len(sentence)
            chunk.append(sentence)

            if token_count >= self.max_tokens:
                combined = ' '.join(chunk)
                new_nodes.append(NodeWithScore(node=TextNode(text=combined), score=None))

                if self.overlap > 0:
                    # Keep last `overlap` sentences for next chunk context
                    chunk = chunk[-self.overlap:]
                    token_count = sum(len(s) for s in chunk)
                else:
                    chunk = []
                    token_count = 0

        if chunk:
            combined = ' '.join(chunk)
            new_nodes.append(NodeWithScore(node=TextNode(text=combined), score=None))

        stats = {
            "total_tokens": total_tokens,
            "total_sentences": total_sentences,
            "chunks_created": len(new_nodes),
            "avg_sentences_per_chunk": round(total_sentences / len(new_nodes), 2) if new_nodes else 0
        }

        return new_nodes, stats

    def __call__(self, node: TextNode) -> tuple[list[NodeWithScore], dict]:
        return self.transform_single_text_node(node)

# === Usage ===
transform = NepaliSentenceChunkTransform(max_tokens=500, overlap=2)  # overlap 2 sentences

chunks, stats = transform(llama_docs[0])

print("🔢 Total tokens:", stats["total_tokens"])
print("🧠 Total sentences:", stats["total_sentences"])
print("📦 Chunks created:", stats["chunks_created"])
print("📊 Avg. sentences per chunk:", stats["avg_sentences_per_chunk"])
print("🪵 -----")
for i, chunk in enumerate(chunks):
    print(f"Chunk {i+1}: {chunk.node.text}")


