"""
Flow-Preserving Nepali Legal Document Chunker

This chunker maintains the natural flow and narrative of legal documents
while creating meaningful chunks that preserve context and readability.
"""

import re
from typing import List, Dict, Any, Optional
from pydantic import Field
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode


class FlowPreservingNepaliChunker(TransformComponent):
    """
    Flow-preserving chunker that maintains document narrative flow.
    
    Features:
    - Preserves natural reading flow
    - Creates larger, more contextual chunks
    - Respects semantic boundaries (।, ?, !)
    - Maintains document coherence
    - Smart paragraph and section transitions
    """
    
    max_tokens: int = Field(default=800, description="Maximum tokens per chunk")
    min_chunk_size: int = Field(default=200, description="Minimum tokens per chunk")
    overlap_sentences: int = Field(default=2, description="Number of sentences to overlap")
    preserve_flow: bool = Field(default=True, description="Maintain document flow")
    
    def __call__(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Main entry point for transformation"""
        return self.transform_single_text_node(node)
    
    def transform_single_text_node(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Transform text while preserving flow"""
        
        # Clean and prepare text
        cleaned_text = self._clean_text(node.text)
        
        # Split into semantic sentences while preserving flow
        sentences = self._split_into_flow_sentences(cleaned_text)
        
        if not sentences:
            return [], {"error": "No valid sentences found"}
        
        # Create flow-preserving chunks
        chunks = self._create_flow_chunks(sentences)
        
        # Convert to nodes
        new_nodes = []
        for i, chunk_text in enumerate(chunks):
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_tokens": len(chunk_text),
                "chunking_method": "flow_preserving",
                "flow_position": self._determine_flow_position(i, len(chunks))
            })
            
            chunk_node = TextNode(text=chunk_text, metadata=chunk_metadata)
            new_nodes.append(NodeWithScore(node=chunk_node, score=None))
        
        # Calculate statistics
        stats = {
            "total_tokens": len(cleaned_text),
            "total_sentences": len(sentences),
            "chunks_created": len(chunks),
            "avg_tokens_per_chunk": round(sum(len(c) for c in chunks) / len(chunks), 2) if chunks else 0,
            "flow_quality": self._calculate_flow_quality(chunks),
            "semantic_boundaries": sum(1 for chunk in chunks if chunk.rstrip().endswith(('।', '?', '!'))),
            "chunking_efficiency": round(len(chunks) / (len(cleaned_text) / self.max_tokens), 2) if chunks else 0
        }
        
        return new_nodes, stats
    
    def _clean_text(self, text: str) -> str:
        """Clean text while preserving structure"""
        # Remove excessive whitespace but keep paragraph breaks
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs to single space
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple line breaks to double
        
        # Clean HTML entities
        text = re.sub(r'&nbsp;', ' ', text)
        text = re.sub(r'&[a-zA-Z]+;', ' ', text)
        
        # Remove page markers but keep content flow
        text = re.sub(r'\d+\s*-->', '', text)
        text = re.sub(r'५२२-->', '', text)
        
        return text.strip()
    
    def _split_into_flow_sentences(self, text: str) -> List[str]:
        """Split text into sentences while maintaining flow"""
        
        # First, split by major semantic boundaries
        major_parts = re.split(r'([।!?])', text)
        
        sentences = []
        current_sentence = ""
        
        i = 0
        while i < len(major_parts):
            part = major_parts[i].strip()
            
            if not part:
                i += 1
                continue
            
            # If this is a punctuation mark
            if part in ['।', '!', '?']:
                current_sentence += part
                # Check if we have a complete meaningful sentence
                if self._is_meaningful_sentence(current_sentence):
                    sentences.append(current_sentence.strip())
                    current_sentence = ""
                else:
                    current_sentence += " "  # Continue building sentence
            else:
                # Add text part
                if current_sentence and not current_sentence.endswith(' '):
                    current_sentence += " "
                current_sentence += part
            
            i += 1
        
        # Add any remaining text
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        # Filter and clean sentences
        filtered_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if self._is_valid_flow_sentence(sentence):
                filtered_sentences.append(sentence)
        
        return filtered_sentences
    
    def _is_meaningful_sentence(self, sentence: str) -> bool:
        """Check if sentence is meaningful enough to be a boundary"""
        sentence = sentence.strip()
        
        # Too short to be meaningful
        if len(sentence) < 30:
            return False
        
        # Must have substantial content
        words = [w for w in sentence.split() if len(w) > 2]
        if len(words) < 5:
            return False
        
        # Check for greeting patterns that shouldn't end sentences
        greeting_patterns = [
            r'^(माननीय|सम्माननीय|आदरणीय)\s+[^।!?]*[।!?]$',
            r'^श्री\s+[^।!?]*[।!?]$'
        ]
        
        for pattern in greeting_patterns:
            if re.match(pattern, sentence):
                return False
        
        return True
    
    def _is_valid_flow_sentence(self, sentence: str) -> bool:
        """Check if sentence should be included in flow"""
        sentence = sentence.strip()
        
        # Minimum length for flow
        if len(sentence) < 20:
            return False
        
        # Skip pure metadata
        metadata_patterns = [
            r'^\d+$',
            r'^[।\.\-\s,;:()]+$',
            r'^&nbsp;$',
            r'^\d+\s*-->',
        ]
        
        for pattern in metadata_patterns:
            if re.match(pattern, sentence):
                return False
        
        return True
    
    def _create_flow_chunks(self, sentences: List[str]) -> List[str]:
        """Create chunks that preserve document flow"""
        
        if not sentences:
            return []
        
        chunks = []
        current_chunk = []
        current_tokens = 0
        
        i = 0
        while i < len(sentences):
            sentence = sentences[i]
            sentence_tokens = len(sentence)
            
            # Check if adding this sentence would exceed limit
            would_exceed = current_tokens + sentence_tokens > self.max_tokens
            
            if would_exceed and current_chunk:
                # We have content and would exceed - check if good breaking point
                if self._is_good_flow_break(current_chunk, sentences, i):
                    # Create chunk
                    chunk_text = self._join_sentences_naturally(current_chunk)
                    if len(chunk_text) >= self.min_chunk_size:
                        chunks.append(chunk_text)
                    
                    # Start new chunk with overlap for flow continuity
                    overlap_sentences = self._get_overlap_sentences(current_chunk)
                    current_chunk = overlap_sentences + [sentence]
                    current_tokens = sum(len(s) for s in current_chunk)
                else:
                    # Not a good break point - include one more sentence if reasonable
                    if sentence_tokens < self.max_tokens * 0.3:
                        current_chunk.append(sentence)
                        current_tokens += sentence_tokens
                    else:
                        # Sentence too long - break anyway
                        chunk_text = self._join_sentences_naturally(current_chunk)
                        if len(chunk_text) >= self.min_chunk_size:
                            chunks.append(chunk_text)
                        
                        overlap_sentences = self._get_overlap_sentences(current_chunk)
                        current_chunk = overlap_sentences + [sentence]
                        current_tokens = sum(len(s) for s in current_chunk)
                
                i += 1
            else:
                # Add sentence to current chunk
                current_chunk.append(sentence)
                current_tokens += sentence_tokens
                i += 1
        
        # Add final chunk
        if current_chunk:
            chunk_text = self._join_sentences_naturally(current_chunk)
            if len(chunk_text) >= self.min_chunk_size:
                chunks.append(chunk_text)
        
        return chunks
    
    def _is_good_flow_break(self, current_chunk: List[str], all_sentences: List[str], next_index: int) -> bool:
        """Determine if this is a good place to break for flow"""
        
        if not current_chunk:
            return True
        
        last_sentence = current_chunk[-1]
        
        # Good break if last sentence ends with strong punctuation
        if last_sentence.rstrip().endswith(('।', '?', '!')):
            # Check if next sentence starts a new topic/section
            if next_index < len(all_sentences):
                next_sentence = all_sentences[next_index]
                
                # Topic change indicators
                topic_changes = [
                    r'^(मुद्दा|निर्णय|फैसला)',  # Case/decision markers
                    r'^(पुनरावेदक|प्रत्यर्थी|वादी)',  # Party changes
                    r'^(सर्वोच्च|उच्च|जिल्ला)',  # Court changes
                    r'^\d+\.',  # Numbered sections
                    r'^(प्रकरण|अध्याय)',  # Chapter/section markers
                ]
                
                for pattern in topic_changes:
                    if re.match(pattern, next_sentence.strip()):
                        return True
            
            return True
        
        return False
    
    def _get_overlap_sentences(self, current_chunk: List[str]) -> List[str]:
        """Get sentences for overlap to maintain flow"""
        if self.overlap_sentences > 0 and len(current_chunk) > self.overlap_sentences:
            return current_chunk[-self.overlap_sentences:]
        return []
    
    def _join_sentences_naturally(self, sentences: List[str]) -> str:
        """Join sentences in a natural way"""
        if not sentences:
            return ""
        
        # Join with appropriate spacing
        result = ""
        for i, sentence in enumerate(sentences):
            if i > 0:
                # Add appropriate spacing between sentences
                if result.rstrip().endswith(('।', '?', '!')):
                    result += "\n\n" + sentence
                else:
                    result += " " + sentence
            else:
                result = sentence
        
        return result.strip()
    
    def _determine_flow_position(self, chunk_index: int, total_chunks: int) -> str:
        """Determine position in document flow"""
        if chunk_index == 0:
            return "beginning"
        elif chunk_index == total_chunks - 1:
            return "end"
        elif chunk_index < total_chunks * 0.3:
            return "early"
        elif chunk_index > total_chunks * 0.7:
            return "late"
        else:
            return "middle"
    
    def _calculate_flow_quality(self, chunks: List[str]) -> float:
        """Calculate how well the chunks preserve flow (0-1)"""
        if not chunks:
            return 0.0
        
        quality_score = 0.0
        
        # Check semantic endings
        semantic_endings = sum(1 for chunk in chunks if chunk.rstrip().endswith(('।', '?', '!')))
        quality_score += (semantic_endings / len(chunks)) * 0.4
        
        # Check chunk size consistency
        lengths = [len(chunk) for chunk in chunks]
        avg_length = sum(lengths) / len(lengths)
        size_consistency = 1 - (max(lengths) - min(lengths)) / (avg_length * 2)
        quality_score += max(0, size_consistency) * 0.3
        
        # Check for natural transitions
        natural_transitions = 0
        for i in range(len(chunks) - 1):
            if self._has_natural_transition(chunks[i], chunks[i + 1]):
                natural_transitions += 1
        
        if len(chunks) > 1:
            quality_score += (natural_transitions / (len(chunks) - 1)) * 0.3
        
        return round(quality_score, 2)
    
    def _has_natural_transition(self, chunk1: str, chunk2: str) -> bool:
        """Check if transition between chunks is natural"""
        # Check if first chunk ends well
        if not chunk1.rstrip().endswith(('।', '?', '!')):
            return False
        
        # Check if second chunk starts appropriately
        chunk2_start = chunk2.strip()[:50]
        
        # Good starting patterns
        good_starts = [
            r'^[A-Za-z\u0900-\u097F]',  # Starts with letter
            r'^\d+\.',  # Numbered point
            r'^(तसर्थ|यसैले|अतः)',  # Conclusion words
        ]
        
        for pattern in good_starts:
            if re.match(pattern, chunk2_start):
                return True
        
        return False


def create_flow_preserving_chunker(
    max_tokens: int = 800,
    min_chunk_size: int = 200,
    overlap_sentences: int = 2
) -> FlowPreservingNepaliChunker:
    """Factory function to create a flow-preserving chunker"""
    return FlowPreservingNepaliChunker(
        max_tokens=max_tokens,
        min_chunk_size=min_chunk_size,
        overlap_sentences=overlap_sentences,
        preserve_flow=True
    )
