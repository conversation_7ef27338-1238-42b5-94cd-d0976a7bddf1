"""
JSON Logging System for Nepali Legal Document Chunking

This module provides comprehensive logging of chunking results in JSON format
for analysis and debugging of the chunking strategy.
"""

import json
import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from llama_index.core.schema import NodeWithScore, TextNode


class ChunkingLogger:
    """
    Logger for chunking operations with JSON output
    """
    
    def __init__(self, log_dir: str = "logs/chunking"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
    def log_chunking_session(
        self,
        document_id: str,
        original_text: str,
        chunks: List[NodeWithScore],
        stats: Dict[str, Any],
        chunking_method: str = "structure_aware",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Log a complete chunking session to JSON
        
        Returns:
            Path to the generated log file
        """
        
        timestamp = datetime.datetime.now().isoformat()
        session_id = f"{document_id}_{timestamp.replace(':', '-').replace('.', '-')}"
        
        # Prepare chunk data
        chunk_data = []
        for i, chunk in enumerate(chunks):
            chunk_info = self._extract_chunk_info(chunk, i)
            chunk_data.append(chunk_info)
        
        # Prepare session log
        session_log = {
            "session_info": {
                "session_id": session_id,
                "document_id": document_id,
                "timestamp": timestamp,
                "chunking_method": chunking_method,
                "metadata": metadata or {}
            },
            "document_info": {
                "original_length": len(original_text),
                "original_lines": len(original_text.split('\n')),
                "original_words": len(original_text.split()),
                "preview": original_text[:200] + "..." if len(original_text) > 200 else original_text
            },
            "chunking_stats": stats,
            "chunks": chunk_data,
            "analysis": self._analyze_chunks(chunks, original_text)
        }
        
        # Save to file
        log_file = self.log_dir / f"{session_id}.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(session_log, f, ensure_ascii=False, indent=2)
        
        return str(log_file)
    
    def _extract_chunk_info(self, chunk: NodeWithScore, index: int) -> Dict[str, Any]:
        """Extract detailed information from a chunk"""
        
        chunk_text = chunk.node.text
        chunk_metadata = chunk.node.metadata or {}
        
        # Analyze chunk content
        lines = [line.strip() for line in chunk_text.split('\n') if line.strip()]
        sentences = [s.strip() for s in chunk_text.split('।') if s.strip()]
        
        # Check semantic quality
        ends_semantically = chunk_text.rstrip().endswith(('।', '?', '!'))
        starts_properly = self._starts_properly(chunk_text)
        
        # Extract key phrases
        key_phrases = self._extract_key_phrases(chunk_text)
        
        return {
            "chunk_id": index,
            "metadata": chunk_metadata,
            "content": {
                "text": chunk_text,
                "length": len(chunk_text),
                "lines": len(lines),
                "sentences": len(sentences),
                "words": len(chunk_text.split())
            },
            "structure": {
                "first_line": lines[0] if lines else "",
                "last_line": lines[-1] if lines else "",
                "starts_properly": starts_properly,
                "ends_semantically": ends_semantically
            },
            "analysis": {
                "key_phrases": key_phrases,
                "section_markers": self._find_section_markers(chunk_text),
                "legal_terms": self._find_legal_terms(chunk_text),
                "quality_score": self._calculate_quality_score(chunk_text, chunk_metadata)
            }
        }
    
    def _starts_properly(self, text: str) -> bool:
        """Check if chunk starts properly (not mid-sentence)"""
        first_line = text.strip().split('\n')[0] if text.strip() else ""
        
        # Good starting patterns
        good_starts = [
            r'^निर्णय नं',
            r'^भाग:',
            r'^फैसला',
            r'^सर्वोच्च',
            r'^मुद्दा',
            r'^पुनरावेदक',
            r'^प्रत्यर्थी',
            r'^विरूद्ध',
            r'^\(प्रकरण',
            r'^उक्त रायमा',
            r'^न्या\.',
            r'^इजलास',
            r'^भर्खरै',
            r'^धेरै'
        ]
        
        import re
        for pattern in good_starts:
            if re.match(pattern, first_line):
                return True
        
        # Check if starts with capital letter or Devanagari
        if first_line and (first_line[0].isupper() or '\u0900' <= first_line[0] <= '\u097F'):
            return True
        
        return False
    
    def _extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases from chunk text"""
        import re
        
        key_patterns = [
            r'निर्णय नं\. \d+[^।]*',
            r'फैसला मिति[^।]*',
            r'मुद्दा:-[^।]*',
            r'प्रकरण नं\.\d+[^।]*',
            r'सर्वोच्च अदालत[^।]*',
            r'काठमाडौं जिल्ला[^।]*',
            r'हदम्याद[^।]*',
            r'दूषित दर्ता[^।]*'
        ]
        
        phrases = []
        for pattern in key_patterns:
            matches = re.findall(pattern, text)
            phrases.extend(matches)
        
        return phrases[:5]  # Return top 5
    
    def _find_section_markers(self, text: str) -> List[str]:
        """Find section markers in the text"""
        import re
        
        markers = []
        
        # प्रकरण markers
        prakarans = re.findall(r'\(प्रकरण नं\.\d+\)', text)
        markers.extend(prakarans)
        
        # Case numbers
        case_nums = re.findall(r'\d{3}-[A-Z]+-\d+', text)
        markers.extend(case_nums)
        
        # Decision markers
        if 'फैसला' in text:
            markers.append('फैसला_section')
        
        if '&nbsp;' in text:
            markers.append('section_separator')
        
        return markers
    
    def _find_legal_terms(self, text: str) -> List[str]:
        """Find legal terms in the text"""
        legal_terms = [
            'हदम्याद', 'दूषित दर्ता', 'विबन्धन', 'पुनरावेदन', 'फिराद',
            'वादी', 'प्रतिवादी', 'न्यायाधीश', 'अदालत', 'निर्णय',
            'फैसला', 'मुद्दा', 'प्रमाण', 'जग्गा', 'नामसारी'
        ]
        
        found_terms = []
        for term in legal_terms:
            if term in text:
                found_terms.append(term)
        
        return found_terms
    
    def _calculate_quality_score(self, text: str, metadata: Dict[str, Any]) -> float:
        """Calculate quality score for the chunk (0-1)"""
        score = 0.0
        
        # Length appropriateness (0.3)
        length = len(text)
        if 100 <= length <= 800:
            score += 0.3
        elif 50 <= length < 100 or 800 < length <= 1200:
            score += 0.2
        elif length >= 50:
            score += 0.1
        
        # Semantic boundaries (0.3)
        if text.rstrip().endswith(('।', '?', '!')):
            score += 0.3
        elif text.rstrip().endswith((':', '।')):
            score += 0.2
        
        # Structure preservation (0.2)
        section_type = metadata.get('section_type', '')
        if section_type in ['header', 'case_info', 'decision', 'footer']:
            score += 0.2
        elif section_type:
            score += 0.1
        
        # Content coherence (0.2)
        if self._starts_properly(text):
            score += 0.1
        
        # Has meaningful content
        if len([w for w in text.split() if len(w) > 3]) >= 5:
            score += 0.1
        
        return round(score, 2)
    
    def _analyze_chunks(self, chunks: List[NodeWithScore], original_text: str) -> Dict[str, Any]:
        """Analyze overall chunking quality"""
        
        if not chunks:
            return {"error": "No chunks to analyze"}
        
        # Basic metrics
        total_chunks = len(chunks)
        total_chunk_length = sum(len(chunk.node.text) for chunk in chunks)
        avg_chunk_length = total_chunk_length / total_chunks
        
        # Quality metrics
        semantic_endings = sum(1 for chunk in chunks 
                             if chunk.node.text.rstrip().endswith(('।', '?', '!')))
        proper_starts = sum(1 for chunk in chunks 
                          if self._starts_properly(chunk.node.text))
        
        # Section distribution
        section_types = {}
        quality_scores = []
        
        for chunk in chunks:
            metadata = chunk.node.metadata or {}
            section_type = metadata.get('section_type', 'unknown')
            section_types[section_type] = section_types.get(section_type, 0) + 1
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(chunk.node.text, metadata)
            quality_scores.append(quality_score)
        
        return {
            "chunk_metrics": {
                "total_chunks": total_chunks,
                "avg_chunk_length": round(avg_chunk_length, 2),
                "length_coverage": round(total_chunk_length / len(original_text), 3),
                "semantic_endings_pct": round(semantic_endings / total_chunks * 100, 1),
                "proper_starts_pct": round(proper_starts / total_chunks * 100, 1)
            },
            "section_distribution": section_types,
            "quality_metrics": {
                "avg_quality_score": round(sum(quality_scores) / len(quality_scores), 2),
                "min_quality_score": min(quality_scores),
                "max_quality_score": max(quality_scores),
                "high_quality_chunks": sum(1 for score in quality_scores if score >= 0.8)
            },
            "recommendations": self._generate_recommendations(chunks, quality_scores)
        }
    
    def _generate_recommendations(self, chunks: List[NodeWithScore], quality_scores: List[float]) -> List[str]:
        """Generate recommendations for improving chunking"""
        recommendations = []
        
        avg_quality = sum(quality_scores) / len(quality_scores)
        
        if avg_quality < 0.6:
            recommendations.append("Consider adjusting chunk size parameters")
        
        semantic_endings = sum(1 for chunk in chunks 
                             if chunk.node.text.rstrip().endswith(('।', '?', '!')))
        if semantic_endings / len(chunks) < 0.7:
            recommendations.append("Improve semantic boundary detection")
        
        very_short_chunks = sum(1 for chunk in chunks if len(chunk.node.text) < 100)
        if very_short_chunks > len(chunks) * 0.3:
            recommendations.append("Too many short chunks - increase min_chunk_size")
        
        very_long_chunks = sum(1 for chunk in chunks if len(chunk.node.text) > 1000)
        if very_long_chunks > 0:
            recommendations.append("Some chunks too long - improve section splitting")
        
        return recommendations


def create_chunking_logger(log_dir: str = "logs/chunking") -> ChunkingLogger:
    """Factory function to create a chunking logger"""
    return ChunkingLogger(log_dir)
