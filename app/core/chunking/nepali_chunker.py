"""
Production-ready Nepali Text Chunking for Legal Documents

This module provides optimized sentence splitting and chunking for Nepali legal documents
specifically designed for the legal_backend application.
"""

import re
from typing import List, Dict, Any, Optional
from pydantic import Field, BaseModel
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode


class NepaliLegalDocumentChunker(TransformComponent):
    """
    Production Nepali sentence chunker for legal documents.
    
    Optimized for:
    - Nepali legal document structure
    - Better sentence boundary detection
    - Noise filtering and cleanup
    - Metadata preservation
    - Configurable chunk sizes
    """
    
    max_tokens: int = Field(default=500, description="Maximum tokens per chunk")
    overlap: int = Field(default=2, description="Number of sentences to overlap between chunks")
    min_chunk_size: int = Field(default=100, description="Minimum tokens per chunk")
    clean_text: bool = Field(default=True, description="Clean and normalize text")

    def split_sentences(self, text: str) -> List[str]:
        """
        Semantic sentence splitting for Nepali legal text.

        Features:
        - Smart ! handling (only split after 3+ words)
        - Semantic boundaries (।, ?, meaningful !)
        - Preserve complete thoughts

        Args:
            text: Input Nepali text

        Returns:
            List of semantically meaningful sentences
        """
        if not text or not text.strip():
            return []

        # Clean text if enabled
        if self.clean_text:
            text = self._clean_text(text)

        # Smart sentence splitting with semantic awareness
        sentences = self._smart_sentence_split(text)

        # Filter and validate
        valid_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if self._is_valid_sentence(sentence):
                valid_sentences.append(sentence)

        return valid_sentences

    def _smart_sentence_split(self, text: str) -> List[str]:
        """
        Smart sentence splitting with context awareness
        """
        sentences = []
        current_sentence = ""

        # Split by lines first to handle paragraph structure
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                if current_sentence.strip():
                    sentences.append(current_sentence.strip())
                    current_sentence = ""
                continue

            # Process each line for sentence boundaries
            line_sentences = self._split_line_semantically(line)

            for i, line_sentence in enumerate(line_sentences):
                if i == 0 and current_sentence:
                    # Continue previous sentence
                    current_sentence += " " + line_sentence
                else:
                    # Start new sentence
                    if current_sentence.strip():
                        sentences.append(current_sentence.strip())
                    current_sentence = line_sentence

        # Add final sentence
        if current_sentence.strip():
            sentences.append(current_sentence.strip())

        return sentences

    def _split_line_semantically(self, line: str) -> List[str]:
        """
        Split a line into semantic sentences with smart punctuation handling
        """
        sentences = []
        current = ""
        words = line.split()

        i = 0
        while i < len(words):
            word = words[i]
            current += word + " "

            # Check for sentence endings
            if self._is_sentence_boundary(word, current, i):
                sentences.append(current.strip())
                current = ""

            i += 1

        # Add remaining text
        if current.strip():
            sentences.append(current.strip())

        return sentences

    def _is_sentence_boundary(self, word: str, current_sentence: str, word_position: int) -> bool:
        """
        Determine if this word marks a sentence boundary

        Smart rules:
        - । (danda) = always sentence boundary
        - ? = always sentence boundary
        - ! = sentence boundary only if after 3+ words (not greetings)
        """
        # Definite sentence enders
        if word.endswith('।') or word.endswith('?'):
            return True

        # Smart exclamation handling
        if word.endswith('!'):
            # Count meaningful words in current sentence
            words_in_sentence = len([w for w in current_sentence.split() if len(w) > 2])

            # Only split on ! if we have 3+ meaningful words (not just greetings)
            if words_in_sentence >= 3:
                return True

            # Special case: if it's clearly a greeting pattern, don't split
            greeting_patterns = [
                r'^(श्री|माननीय|सम्माननीय|आदरणीय)',  # Honorifics
                r'^(नमस्कार|नमस्ते)',  # Greetings
            ]

            for pattern in greeting_patterns:
                if re.match(pattern, current_sentence.strip()):
                    return False

            return True

        return False
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize the text"""
        # Remove HTML entities and tags
        text = re.sub(r'&nbsp;|&amp;|&lt;|&gt;|&quot;', ' ', text)
        text = re.sub(r'<[^>]+>', '', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # Clean up multiple punctuation
        text = re.sub(r'[।]{2,}', '।', text)
        text = re.sub(r'[\.]{3,}', '...', text)
        
        # Remove page markers and navigation elements
        text = re.sub(r'\d+\s*-->', '', text)
        text = re.sub(r'५२२-->', '', text)
        
        return text.strip()
    
    def _is_valid_sentence(self, sentence: str) -> bool:
        """Check if sentence is valid and not noise"""
        sentence = sentence.strip()
        
        # Too short
        if len(sentence) < 20:
            return False
        
        # Check for noise patterns
        if self._is_noise_sentence(sentence):
            return False
        
        # Must contain meaningful content
        meaningful_chars = re.sub(r'[\s\d\.\-।,;:()]+', '', sentence)
        if len(meaningful_chars) < 10:
            return False
        
        return True
    
    def _is_noise_sentence(self, sentence: str) -> bool:
        """Check if sentence is likely noise/metadata"""
        sentence = sentence.strip()
        
        noise_patterns = [
            r'^\d+$',  # Just numbers
            r'^[।\.\-\s,;:()]+$',  # Just punctuation
            r'^भर्खरै प्रकाशित नजिरहरू',  # Recently published
            r'^धेरै हेरिएका नजिरहरु',  # Most viewed
            r'^निर्णय नं:\s*#\s*\d+',  # Decision number headers
            r'^फैसला मिति\s*:\s*\d+',  # Decision date headers
            r'^\d+\s*-->',  # Page markers
            r'^इति संवत्',  # Date endings
            r'^उक्त रायमा सहमत छु',  # Agreement statements
            r'^इजलास अधिकृत',  # Court officer
            r'^न्या\.',  # Judge abbreviation
            r'^मा\.',  # Honorable abbreviation
            r'^\d+\s*\|\s*मुद्दा नं',  # Case number format
        ]
        
        for pattern in noise_patterns:
            if re.match(pattern, sentence):
                return True
        
        return False
    
    def create_semantic_chunks_with_overlap(self, sentences: List[str]) -> List[str]:
        """
        Create semantic chunks that end at complete thoughts/sentences

        Strategy:
        - Always end chunks at sentence boundaries
        - Prefer semantic completeness over strict token limits
        - Allow some flexibility in chunk size for better semantics
        """
        if not sentences:
            return []

        chunks = []
        current_chunk = []
        current_tokens = 0

        i = 0
        while i < len(sentences):
            sentence = sentences[i]
            sentence_tokens = len(sentence)

            # Check if adding this sentence would exceed our target
            would_exceed = current_tokens + sentence_tokens > self.max_tokens

            if would_exceed and current_chunk:
                # We have content and would exceed limit

                # Check if current chunk is semantically complete
                if self._is_semantic_boundary(current_chunk, sentences, i):
                    # Good place to break - create chunk
                    chunk_text = ' '.join(current_chunk)
                    if len(chunk_text.strip()) >= self.min_chunk_size:
                        chunks.append(chunk_text)

                    # Start new chunk with overlap
                    current_chunk = self._create_overlap(current_chunk)
                    current_tokens = sum(len(s) for s in current_chunk)
                else:
                    # Not a good semantic boundary, try to include one more sentence
                    # if it's not too long
                    if sentence_tokens < self.max_tokens * 0.3:  # Max 30% of chunk size
                        current_chunk.append(sentence)
                        current_tokens += sentence_tokens
                        i += 1
                        continue
                    else:
                        # Sentence too long, break anyway
                        chunk_text = ' '.join(current_chunk)
                        if len(chunk_text.strip()) >= self.min_chunk_size:
                            chunks.append(chunk_text)

                        current_chunk = self._create_overlap(current_chunk)
                        current_tokens = sum(len(s) for s in current_chunk)
            else:
                # Add sentence to current chunk
                current_chunk.append(sentence)
                current_tokens += sentence_tokens
                i += 1

        # Add final chunk if it has content
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            if len(chunk_text.strip()) >= self.min_chunk_size:
                chunks.append(chunk_text)

        return chunks

    def _is_semantic_boundary(self, current_chunk: List[str], all_sentences: List[str], next_index: int) -> bool:
        """
        Check if this is a good semantic boundary for chunking
        """
        if not current_chunk:
            return False

        last_sentence = current_chunk[-1]

        # Strong semantic boundaries
        strong_endings = [
            '।',  # Danda - definite sentence end
            '?',  # Question
            '!',  # Exclamation (after our smart filtering)
        ]

        # Check if last sentence ends with strong punctuation
        for ending in strong_endings:
            if last_sentence.rstrip().endswith(ending):
                return True

        # Check if next sentence starts a new topic/section
        if next_index < len(all_sentences):
            next_sentence = all_sentences[next_index]

            # Topic change indicators
            topic_starters = [
                r'^(मुद्दा|निर्णय|फैसला|प्रकरण)',  # Case/decision markers
                r'^(पुनरावेदक|प्रत्यर्थी|वादी)',  # Party markers
                r'^(सर्वोच्च|उच्च|जिल्ला)',  # Court markers
                r'^\d+\.',  # Numbered points
            ]

            for pattern in topic_starters:
                if re.match(pattern, next_sentence.strip()):
                    return True

        return False

    def _create_overlap(self, current_chunk: List[str]) -> List[str]:
        """Create overlap sentences for next chunk"""
        if self.overlap > 0 and len(current_chunk) > self.overlap:
            return current_chunk[-self.overlap:]
        else:
            return []
    
    def transform_single_text_node(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Transform a single text node into semantic chunks"""
        sentences = self.split_sentences(node.text)

        if not sentences:
            return [], {"error": "No valid sentences found", "original_length": len(node.text)}

        # Use semantic chunking instead of simple token-based chunking
        chunks = self.create_semantic_chunks_with_overlap(sentences)

        new_nodes = []
        for i, chunk_text in enumerate(chunks):
            # Preserve original metadata and add chunk info
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_tokens": len(chunk_text),
                "chunk_sentences": len([s for s in sentences if s in chunk_text]),
                "source_node_id": getattr(node, 'node_id', None),
                "chunking_method": "nepali_legal_semantic"
            })

            chunk_node = TextNode(
                text=chunk_text,
                metadata=chunk_metadata
            )
            new_nodes.append(NodeWithScore(node=chunk_node, score=None))

        # Calculate semantic quality metrics
        semantic_boundaries = sum(1 for chunk in chunks if self._ends_with_semantic_boundary(chunk))

        stats = {
            "total_tokens": len(node.text),
            "total_sentences": len(sentences),
            "chunks_created": len(chunks),
            "avg_sentences_per_chunk": round(len(sentences) / len(chunks), 2) if chunks else 0,
            "avg_tokens_per_chunk": round(sum(len(c) for c in chunks) / len(chunks), 2) if chunks else 0,
            "semantic_boundaries": semantic_boundaries,
            "semantic_quality": round(semantic_boundaries / len(chunks), 2) if chunks else 0,
            "chunking_efficiency": round(len(chunks) / (len(node.text) / self.max_tokens), 2) if chunks else 0
        }

        return new_nodes, stats

    def _ends_with_semantic_boundary(self, chunk_text: str) -> bool:
        """Check if chunk ends with a semantic boundary"""
        chunk_text = chunk_text.rstrip()
        return chunk_text.endswith(('।', '?', '!'))
    
    def __call__(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Main entry point for transformation"""
        return self.transform_single_text_node(node)


def create_nepali_chunker(
    max_tokens: int = 500,
    overlap: int = 2,
    min_chunk_size: int = 100,
    clean_text: bool = True
) -> NepaliLegalDocumentChunker:
    """
    Factory function to create a configured Nepali chunker.
    
    Args:
        max_tokens: Maximum tokens per chunk
        overlap: Number of sentences to overlap between chunks
        min_chunk_size: Minimum tokens per chunk
        clean_text: Whether to clean and normalize text
        
    Returns:
        Configured NepaliLegalDocumentChunker instance
    """
    return NepaliLegalDocumentChunker(
        max_tokens=max_tokens,
        overlap=overlap,
        min_chunk_size=min_chunk_size,
        clean_text=clean_text
    )


# Example usage for testing
if __name__ == "__main__":
    # Test with sample text
    sample_text = """
    निर्णय नं. १०१६४ - निर्णय दर्ता बदर
    
    सर्वोच्च अदालत, संयुक्त इजलास
    
    मुद्दा:- निर्णय दर्ता बदर
    
    विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने ।
    
    कुनै मुद्दाको रोहमा प्रमाण बुझ्ने प्रसङ्गमा जारी भएको म्याद पक्षले प्राप्त गर्दैमा सो मुद्दामा पछि हुने निर्णयसमेत निजलाई जानकारी थियो भन्न नमिल्ने ।
    """
    
    chunker = create_nepali_chunker(max_tokens=300, overlap=1)
    test_node = TextNode(text=sample_text, metadata={"test": True})
    
    chunks, stats = chunker(test_node)
    
    print(f"Statistics: {stats}")
    print(f"Number of chunks: {len(chunks)}")
    
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1}: {chunk.node.text[:100]}...")
