"""
Production-ready Nepali Text Chunking for Legal Documents

This module provides optimized sentence splitting and chunking for Nepali legal documents
specifically designed for the legal_backend application.
"""

import re
from typing import List, Dict, Any, Optional
from pydantic import Field, BaseModel
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode


class NepaliLegalDocumentChunker(TransformComponent):
    """
    Production Nepali sentence chunker for legal documents.
    
    Optimized for:
    - Nepali legal document structure
    - Better sentence boundary detection
    - Noise filtering and cleanup
    - Metadata preservation
    - Configurable chunk sizes
    """
    
    max_tokens: int = Field(default=500, description="Maximum tokens per chunk")
    overlap: int = Field(default=2, description="Number of sentences to overlap between chunks")
    min_chunk_size: int = Field(default=100, description="Minimum tokens per chunk")
    clean_text: bool = Field(default=True, description="Clean and normalize text")

    def split_sentences(self, text: str) -> List[str]:
        """
        Split Nepali legal text into meaningful sentences.
        
        Args:
            text: Input Nepali text
            
        Returns:
            List of cleaned sentences
        """
        if not text or not text.strip():
            return []
        
        # Clean text if enabled
        if self.clean_text:
            text = self._clean_text(text)
        
        # Split by major punctuation marks
        sentences = []
        parts = re.split(r'[।!?]', text)
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
                
            # Handle long parts by splitting on line breaks
            if len(part) > 300:
                sub_parts = part.split('\n')
                for sub_part in sub_parts:
                    sub_part = sub_part.strip()
                    if self._is_valid_sentence(sub_part):
                        sentences.append(sub_part)
            elif self._is_valid_sentence(part):
                sentences.append(part)
        
        return sentences
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize the text"""
        # Remove HTML entities and tags
        text = re.sub(r'&nbsp;|&amp;|&lt;|&gt;|&quot;', ' ', text)
        text = re.sub(r'<[^>]+>', '', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # Clean up multiple punctuation
        text = re.sub(r'[।]{2,}', '।', text)
        text = re.sub(r'[\.]{3,}', '...', text)
        
        # Remove page markers and navigation elements
        text = re.sub(r'\d+\s*-->', '', text)
        text = re.sub(r'५२२-->', '', text)
        
        return text.strip()
    
    def _is_valid_sentence(self, sentence: str) -> bool:
        """Check if sentence is valid and not noise"""
        sentence = sentence.strip()
        
        # Too short
        if len(sentence) < 20:
            return False
        
        # Check for noise patterns
        if self._is_noise_sentence(sentence):
            return False
        
        # Must contain meaningful content
        meaningful_chars = re.sub(r'[\s\d\.\-।,;:()]+', '', sentence)
        if len(meaningful_chars) < 10:
            return False
        
        return True
    
    def _is_noise_sentence(self, sentence: str) -> bool:
        """Check if sentence is likely noise/metadata"""
        sentence = sentence.strip()
        
        noise_patterns = [
            r'^\d+$',  # Just numbers
            r'^[।\.\-\s,;:()]+$',  # Just punctuation
            r'^भर्खरै प्रकाशित नजिरहरू',  # Recently published
            r'^धेरै हेरिएका नजिरहरु',  # Most viewed
            r'^निर्णय नं:\s*#\s*\d+',  # Decision number headers
            r'^फैसला मिति\s*:\s*\d+',  # Decision date headers
            r'^\d+\s*-->',  # Page markers
            r'^इति संवत्',  # Date endings
            r'^उक्त रायमा सहमत छु',  # Agreement statements
            r'^इजलास अधिकृत',  # Court officer
            r'^न्या\.',  # Judge abbreviation
            r'^मा\.',  # Honorable abbreviation
            r'^\d+\s*\|\s*मुद्दा नं',  # Case number format
        ]
        
        for pattern in noise_patterns:
            if re.match(pattern, sentence):
                return True
        
        return False
    
    def create_chunks_with_overlap(self, sentences: List[str]) -> List[str]:
        """Create chunks with proper overlap"""
        if not sentences:
            return []
        
        chunks = []
        current_chunk = []
        current_tokens = 0
        
        i = 0
        while i < len(sentences):
            sentence = sentences[i]
            sentence_tokens = len(sentence)
            
            # If adding this sentence would exceed max_tokens and we have content
            if current_tokens + sentence_tokens > self.max_tokens and current_chunk:
                # Create chunk from current sentences
                chunk_text = ' '.join(current_chunk)
                if len(chunk_text.strip()) >= self.min_chunk_size:
                    chunks.append(chunk_text)
                
                # Start new chunk with overlap
                if self.overlap > 0 and len(current_chunk) > self.overlap:
                    current_chunk = current_chunk[-self.overlap:]
                    current_tokens = sum(len(s) for s in current_chunk)
                else:
                    current_chunk = []
                    current_tokens = 0
            else:
                # Add sentence to current chunk
                current_chunk.append(sentence)
                current_tokens += sentence_tokens
                i += 1
        
        # Add final chunk if it has content
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            if len(chunk_text.strip()) >= self.min_chunk_size:
                chunks.append(chunk_text)
        
        return chunks
    
    def transform_single_text_node(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Transform a single text node into chunks"""
        sentences = self.split_sentences(node.text)
        
        if not sentences:
            return [], {"error": "No valid sentences found", "original_length": len(node.text)}
        
        chunks = self.create_chunks_with_overlap(sentences)
        
        new_nodes = []
        for i, chunk_text in enumerate(chunks):
            # Preserve original metadata and add chunk info
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_tokens": len(chunk_text),
                "source_node_id": getattr(node, 'node_id', None),
                "chunking_method": "nepali_legal_optimized"
            })
            
            chunk_node = TextNode(
                text=chunk_text,
                metadata=chunk_metadata
            )
            new_nodes.append(NodeWithScore(node=chunk_node, score=None))
        
        stats = {
            "total_tokens": len(node.text),
            "total_sentences": len(sentences),
            "chunks_created": len(chunks),
            "avg_sentences_per_chunk": round(len(sentences) / len(chunks), 2) if chunks else 0,
            "avg_tokens_per_chunk": round(sum(len(c) for c in chunks) / len(chunks), 2) if chunks else 0,
            "chunking_efficiency": round(len(chunks) / (len(node.text) / self.max_tokens), 2) if chunks else 0
        }
        
        return new_nodes, stats
    
    def __call__(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Main entry point for transformation"""
        return self.transform_single_text_node(node)


def create_nepali_chunker(
    max_tokens: int = 500,
    overlap: int = 2,
    min_chunk_size: int = 100,
    clean_text: bool = True
) -> NepaliLegalDocumentChunker:
    """
    Factory function to create a configured Nepali chunker.
    
    Args:
        max_tokens: Maximum tokens per chunk
        overlap: Number of sentences to overlap between chunks
        min_chunk_size: Minimum tokens per chunk
        clean_text: Whether to clean and normalize text
        
    Returns:
        Configured NepaliLegalDocumentChunker instance
    """
    return NepaliLegalDocumentChunker(
        max_tokens=max_tokens,
        overlap=overlap,
        min_chunk_size=min_chunk_size,
        clean_text=clean_text
    )


# Example usage for testing
if __name__ == "__main__":
    # Test with sample text
    sample_text = """
    निर्णय नं. १०१६४ - निर्णय दर्ता बदर
    
    सर्वोच्च अदालत, संयुक्त इजलास
    
    मुद्दा:- निर्णय दर्ता बदर
    
    विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने ।
    
    कुनै मुद्दाको रोहमा प्रमाण बुझ्ने प्रसङ्गमा जारी भएको म्याद पक्षले प्राप्त गर्दैमा सो मुद्दामा पछि हुने निर्णयसमेत निजलाई जानकारी थियो भन्न नमिल्ने ।
    """
    
    chunker = create_nepali_chunker(max_tokens=300, overlap=1)
    test_node = TextNode(text=sample_text, metadata={"test": True})
    
    chunks, stats = chunker(test_node)
    
    print(f"Statistics: {stats}")
    print(f"Number of chunks: {len(chunks)}")
    
    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1}: {chunk.node.text[:100]}...")
