"""
Structure-aware Nepali Legal Document Chunker

This chunker understands the specific structure of Nepali legal documents
and creates semantic chunks based on document sections.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from pydantic import Field, BaseModel
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode


class NepaliLegalStructureChunker(TransformComponent):
    """
    Structure-aware chunker for Nepali legal documents.
    
    Recognizes document sections:
    1. <PERSON><PERSON> (title, court, judges)
    2. Case Info (parties, case numbers) 
    3. Main Content (legal reasoning, प्रकरण sections)
    4. Decision (फैसला section)
    5. Footer (signatures, recent cases)
    """
    
    max_tokens: int = Field(default=800, description="Maximum tokens per chunk")
    min_chunk_size: int = Field(default=100, description="Minimum tokens per chunk")
    preserve_structure: bool = Field(default=True, description="Preserve document structure")
    
    def __call__(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Main entry point for transformation"""
        return self.transform_single_text_node(node)
    
    def transform_single_text_node(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Transform a single text node into structure-aware chunks"""
        
        # Parse document structure
        sections = self._parse_document_structure(node.text)
        
        if not sections:
            return [], {"error": "No valid sections found", "original_length": len(node.text)}
        
        # Create chunks based on structure
        chunks = self._create_structure_based_chunks(sections)
        
        new_nodes = []
        for i, chunk_data in enumerate(chunks):
            # Preserve original metadata and add chunk info
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_tokens": len(chunk_data["text"]),
                "section_type": chunk_data["section_type"],
                "section_title": chunk_data.get("title", ""),
                "source_node_id": getattr(node, 'node_id', None),
                "chunking_method": "nepali_legal_structure_aware"
            })
            
            chunk_node = TextNode(
                text=chunk_data["text"],
                metadata=chunk_metadata
            )
            new_nodes.append(NodeWithScore(node=chunk_node, score=None))
        
        stats = {
            "total_tokens": len(node.text),
            "sections_found": len(sections),
            "chunks_created": len(chunks),
            "avg_tokens_per_chunk": round(sum(len(c["text"]) for c in chunks) / len(chunks), 2) if chunks else 0,
            "section_types": [s["type"] for s in sections]
        }
        
        return new_nodes, stats
    
    def _parse_document_structure(self, text: str) -> List[Dict[str, Any]]:
        """
        Parse the document into structural sections
        """
        lines = text.split('\n')
        sections = []
        current_section = None
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Skip empty lines and &nbsp;
            if not line or line == '&nbsp;':
                continue
            
            # Detect section boundaries
            section_type = self._detect_section_type(line, i, lines)
            
            if section_type:
                # Save previous section
                if current_section:
                    sections.append(current_section)
                
                # Start new section
                current_section = {
                    "type": section_type,
                    "title": line if section_type != "content" else "",
                    "content": [line],
                    "start_line": i
                }
            elif current_section:
                # Add to current section
                current_section["content"].append(line)
        
        # Add final section
        if current_section:
            sections.append(current_section)
        
        return sections
    
    def _detect_section_type(self, line: str, line_num: int, all_lines: List[str]) -> Optional[str]:
        """
        Detect what type of section this line starts
        """
        line_clean = line.strip()
        
        # Header patterns (first part of document)
        if line_num < 20:
            if re.match(r'^निर्णय नं\. \d+', line_clean):
                return "header"
            elif re.match(r'^भाग:', line_clean):
                return "header"
            elif re.match(r'^फैसला मिति', line_clean):
                return "header"
            elif re.match(r'^सर्वोच्च अदालत', line_clean):
                return "header"
            elif re.match(r'^(सम्माननीय|माननीय)', line_clean):
                return "header"
        
        # Case info patterns
        if re.match(r'^मुद्दा:-', line_clean):
            return "case_info"
        elif re.match(r'^\d{3}-[A-Z]+-\d+', line_clean):
            return "case_info"
        elif re.match(r'^पुनरावेदक / प्रतिवादी', line_clean):
            return "case_info"
        elif re.match(r'^प्रत्यर्थी / वादी', line_clean):
            return "case_info"
        elif re.match(r'^विरूद्ध$', line_clean):
            return "case_info"
        
        # Main decision section
        if line_clean == 'फैसला':
            return "decision"
        
        # Content sections (प्रकरण)
        if re.match(r'^\(प्रकरण नं\.\d+\)', line_clean):
            return "content_section"
        
        # Footer patterns
        if re.match(r'^उक्त रायमा सहमत छु', line_clean):
            return "footer"
        elif re.match(r'^न्या\.', line_clean):
            return "footer"
        elif re.match(r'^इजलास अधिकृत', line_clean):
            return "footer"
        elif re.match(r'^इति संवत्', line_clean):
            return "footer"
        elif re.match(r'^भर्खरै प्रकाशित नजिरहरू', line_clean):
            return "footer"
        elif re.match(r'^धेरै हेरिएका नजिरहरु', line_clean):
            return "footer"
        
        return None
    
    def _create_structure_based_chunks(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create chunks based on document structure
        """
        chunks = []
        
        # Group related sections
        section_groups = self._group_sections(sections)
        
        for group in section_groups:
            group_text = self._combine_section_group(group)
            
            # If group is too large, split it semantically
            if len(group_text) > self.max_tokens:
                sub_chunks = self._split_large_section(group, group_text)
                chunks.extend(sub_chunks)
            else:
                chunks.append({
                    "text": group_text,
                    "section_type": group["type"],
                    "title": group.get("title", ""),
                    "sections": len(group["sections"])
                })
        
        return chunks
    
    def _group_sections(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Group related sections together
        """
        groups = []
        current_group = None
        
        for section in sections:
            section_type = section["type"]
            
            # Start new group for major section changes
            if (not current_group or 
                section_type in ["header", "decision", "footer"] or
                (current_group["type"] != section_type and section_type != "content")):
                
                if current_group:
                    groups.append(current_group)
                
                current_group = {
                    "type": section_type,
                    "title": section.get("title", ""),
                    "sections": [section]
                }
            else:
                # Add to current group
                current_group["sections"].append(section)
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _combine_section_group(self, group: Dict[str, Any]) -> str:
        """
        Combine sections in a group into text
        """
        all_content = []
        
        for section in group["sections"]:
            section_text = '\n'.join(section["content"])
            all_content.append(section_text)
        
        return '\n\n'.join(all_content)
    
    def _split_large_section(self, group: Dict[str, Any], text: str) -> List[Dict[str, Any]]:
        """
        Split large sections semantically
        """
        chunks = []
        
        # For decision sections, split by प्रकरण or paragraph
        if group["type"] in ["decision", "content_section"]:
            # Split by प्रकरण markers
            parts = re.split(r'(\(प्रकरण नं\.\d+\))', text)
            
            current_chunk = ""
            current_title = ""
            
            for part in parts:
                part = part.strip()
                if not part:
                    continue
                
                # Check if this is a प्रकरण marker
                if re.match(r'^\(प्रकरण नं\.\d+\)$', part):
                    # Save previous chunk if it exists
                    if current_chunk and len(current_chunk) >= self.min_chunk_size:
                        chunks.append({
                            "text": current_chunk.strip(),
                            "section_type": group["type"],
                            "title": current_title,
                            "sections": 1
                        })
                    
                    # Start new chunk
                    current_chunk = part + "\n\n"
                    current_title = part
                else:
                    # Add to current chunk
                    if len(current_chunk + part) > self.max_tokens and current_chunk:
                        # Save current chunk
                        chunks.append({
                            "text": current_chunk.strip(),
                            "section_type": group["type"],
                            "title": current_title,
                            "sections": 1
                        })
                        current_chunk = part + "\n\n"
                        current_title = ""
                    else:
                        current_chunk += part + "\n\n"
            
            # Add final chunk
            if current_chunk and len(current_chunk) >= self.min_chunk_size:
                chunks.append({
                    "text": current_chunk.strip(),
                    "section_type": group["type"],
                    "title": current_title,
                    "sections": 1
                })
        
        else:
            # For other sections, split by sentences
            sentences = re.split(r'[।!?]', text)
            current_chunk = ""
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                if len(current_chunk + sentence) > self.max_tokens and current_chunk:
                    chunks.append({
                        "text": current_chunk.strip(),
                        "section_type": group["type"],
                        "title": group.get("title", ""),
                        "sections": 1
                    })
                    current_chunk = sentence + "।"
                else:
                    current_chunk += sentence + "।"
            
            if current_chunk and len(current_chunk) >= self.min_chunk_size:
                chunks.append({
                    "text": current_chunk.strip(),
                    "section_type": group["type"],
                    "title": group.get("title", ""),
                    "sections": 1
                })
        
        return chunks if chunks else [{
            "text": text,
            "section_type": group["type"],
            "title": group.get("title", ""),
            "sections": len(group["sections"])
        }]


def create_structure_aware_chunker(
    max_tokens: int = 800,
    min_chunk_size: int = 100,
    preserve_structure: bool = True
) -> NepaliLegalStructureChunker:
    """
    Factory function to create a structure-aware chunker.
    """
    return NepaliLegalStructureChunker(
        max_tokens=max_tokens,
        min_chunk_size=min_chunk_size,
        preserve_structure=preserve_structure
    )
