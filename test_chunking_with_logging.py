"""
Test the new chunking strategy with comprehensive JSON logging
"""

import asyncio
import os
import json
from pathlib import Path
from pymongo import AsyncMongoClient
from llama_index.core.schema import TextNode

# Import our chunking modules
from app.core.chunking.structure_aware_chunker import create_structure_aware_chunker
from app.core.chunking.chunking_logger import create_chunking_logger


async def test_chunking_with_database_logging():
    """Test chunking with real database documents and JSON logging"""
    
    print("🚀 TESTING NEW CHUNKING STRATEGY WITH JSON LOGGING")
    print("=" * 70)
    
    # Setup
    uri = "mongodb://172.16.16.54:8300/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.2.10"
    client = AsyncMongoClient(uri)
    db = client["legal_backend_db"]
    collection = db["documents"]
    
    # Create chunker and logger
    chunker = create_structure_aware_chunker(
        max_tokens=600,
        min_chunk_size=100,
        preserve_structure=True
    )
    logger = create_chunking_logger("logs/chunking")
    
    # Get sample documents
    docs = await collection.find().limit(3).to_list(length=3)
    
    if not docs:
        print("❌ No documents found in database")
        return
    
    print(f"📄 Processing {len(docs)} documents...")
    
    all_results = []
    
    for i, doc in enumerate(docs):
        print(f"\n📝 Processing Document {i+1}")
        print("-" * 40)
        
        # Extract document info
        doc_id = str(doc.get('_id', f'doc_{i+1}'))
        doc_text = doc['text']
        doc_metadata = doc.get('metadata', {})
        
        print(f"Document ID: {doc_id}")
        print(f"Original length: {len(doc_text)} characters")
        print(f"Court type: {doc_metadata.get('court_type', 'Unknown')}")
        
        # Create TextNode
        text_node = TextNode(
            text=doc_text,
            metadata=doc_metadata
        )
        
        # Process with chunker
        try:
            chunks, stats = chunker(text_node)
            
            print(f"✅ Chunking successful!")
            print(f"   Sections found: {stats.get('sections_found', 0)}")
            print(f"   Chunks created: {stats.get('chunks_created', 0)}")
            print(f"   Section types: {stats.get('section_types', [])}")
            
            # Log to JSON
            log_file = logger.log_chunking_session(
                document_id=doc_id,
                original_text=doc_text,
                chunks=chunks,
                stats=stats,
                chunking_method="structure_aware_v1",
                metadata={
                    "court_type": doc_metadata.get('court_type'),
                    "case_type": doc_metadata.get('case_type'),
                    "processing_date": "2024-01-30"
                }
            )
            
            print(f"📊 JSON log saved: {log_file}")
            
            # Show chunk preview
            print(f"\n📋 Chunk Preview:")
            for j, chunk in enumerate(chunks[:2]):  # Show first 2 chunks
                chunk_metadata = chunk.node.metadata
                section_type = chunk_metadata.get('section_type', 'unknown')
                chunk_preview = chunk.node.text[:100].replace('\n', ' ')
                print(f"   {j+1}. [{section_type}] {chunk_preview}...")
            
            all_results.append({
                "document_id": doc_id,
                "log_file": log_file,
                "stats": stats,
                "chunks_count": len(chunks)
            })
            
        except Exception as e:
            print(f"❌ Error processing document: {e}")
            continue
    
    await client.close()
    
    # Generate summary report
    print(f"\n📊 PROCESSING SUMMARY")
    print("=" * 50)
    
    total_chunks = sum(r['chunks_count'] for r in all_results)
    avg_chunks = total_chunks / len(all_results) if all_results else 0
    
    print(f"Documents processed: {len(all_results)}")
    print(f"Total chunks created: {total_chunks}")
    print(f"Average chunks per document: {avg_chunks:.1f}")
    
    # Show log files
    print(f"\n📁 Generated Log Files:")
    for result in all_results:
        print(f"   {result['document_id']}: {result['log_file']}")
    
    return all_results


def test_with_article_file():
    """Test with the article.txt file"""
    
    print("\n\n📄 TESTING WITH ARTICLE.TXT FILE")
    print("=" * 50)
    
    # Read article file
    article_path = "app/core/chunking/article.txt"
    if not Path(article_path).exists():
        print(f"❌ Article file not found: {article_path}")
        return
    
    with open(article_path, 'r', encoding='utf-8') as f:
        article_text = f.read()
    
    print(f"📄 Article length: {len(article_text)} characters")
    
    # Create chunker and logger
    chunker = create_structure_aware_chunker(
        max_tokens=500,
        min_chunk_size=80,
        preserve_structure=True
    )
    logger = create_chunking_logger("logs/chunking")
    
    # Create TextNode
    text_node = TextNode(
        text=article_text,
        metadata={
            "source": "article.txt",
            "document_type": "legal_decision",
            "test_case": True
        }
    )
    
    # Process
    chunks, stats = chunker(text_node)
    
    print(f"✅ Processing complete!")
    print(f"   Sections found: {stats.get('sections_found', 0)}")
    print(f"   Chunks created: {stats.get('chunks_created', 0)}")
    print(f"   Section types: {stats.get('section_types', [])}")
    
    # Log to JSON
    log_file = logger.log_chunking_session(
        document_id="article_txt_sample",
        original_text=article_text,
        chunks=chunks,
        stats=stats,
        chunking_method="structure_aware_v1",
        metadata={
            "source_file": "article.txt",
            "test_type": "file_based",
            "processing_date": "2024-01-30"
        }
    )
    
    print(f"📊 JSON log saved: {log_file}")
    
    # Show detailed chunk analysis
    print(f"\n📋 DETAILED CHUNK ANALYSIS:")
    print("-" * 40)
    
    for i, chunk in enumerate(chunks):
        chunk_metadata = chunk.node.metadata
        chunk_text = chunk.node.text
        
        section_type = chunk_metadata.get('section_type', 'unknown')
        chunk_tokens = chunk_metadata.get('chunk_tokens', len(chunk_text))
        
        # Check quality
        ends_well = chunk_text.rstrip().endswith(('।', '?', '!'))
        has_content = len([w for w in chunk_text.split() if len(w) > 3]) >= 5
        
        print(f"\nChunk {i+1}: {section_type.upper()}")
        print(f"   Length: {chunk_tokens} chars")
        print(f"   Ends semantically: {'✅' if ends_well else '❌'}")
        print(f"   Has content: {'✅' if has_content else '❌'}")
        print(f"   Preview: {chunk_text[:80].replace(chr(10), ' ')}...")
    
    return log_file


def analyze_json_logs():
    """Analyze the generated JSON logs"""
    
    print("\n\n🔍 ANALYZING JSON LOGS")
    print("=" * 40)
    
    log_dir = Path("logs/chunking")
    if not log_dir.exists():
        print("❌ No log directory found")
        return
    
    json_files = list(log_dir.glob("*.json"))
    if not json_files:
        print("❌ No JSON log files found")
        return
    
    print(f"📁 Found {len(json_files)} log files")
    
    # Analyze each log file
    for log_file in json_files[:3]:  # Analyze first 3
        print(f"\n📊 Analyzing: {log_file.name}")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        # Extract key metrics
        session_info = log_data.get('session_info', {})
        chunking_stats = log_data.get('chunking_stats', {})
        analysis = log_data.get('analysis', {})
        chunks = log_data.get('chunks', [])
        
        print(f"   Document ID: {session_info.get('document_id', 'unknown')}")
        print(f"   Chunking method: {session_info.get('chunking_method', 'unknown')}")
        print(f"   Chunks created: {len(chunks)}")
        
        # Quality metrics
        chunk_metrics = analysis.get('chunk_metrics', {})
        quality_metrics = analysis.get('quality_metrics', {})
        
        print(f"   Avg chunk length: {chunk_metrics.get('avg_chunk_length', 0):.0f}")
        print(f"   Semantic endings: {chunk_metrics.get('semantic_endings_pct', 0):.1f}%")
        print(f"   Avg quality score: {quality_metrics.get('avg_quality_score', 0):.2f}")
        
        # Section distribution
        section_dist = analysis.get('section_distribution', {})
        if section_dist:
            print(f"   Section types: {dict(section_dist)}")
        
        # Recommendations
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            print(f"   Recommendations: {recommendations}")


if __name__ == "__main__":
    print("🧪 COMPREHENSIVE CHUNKING STRATEGY TEST")
    print("=" * 70)
    
    # Test with article file first
    try:
        test_with_article_file()
    except Exception as e:
        print(f"❌ Article test failed: {e}")
    
    # Test with database
    print("\n" + "="*70)
    try:
        asyncio.run(test_chunking_with_database_logging())
    except Exception as e:
        print(f"❌ Database test failed: {e}")
    
    # Analyze generated logs
    try:
        analyze_json_logs()
    except Exception as e:
        print(f"❌ Log analysis failed: {e}")
    
    print("\n✅ Testing completed!")
    print("\n📋 WHAT WAS TESTED:")
    print("1. Structure-aware chunking with semantic boundaries")
    print("2. Smart exclamation mark handling")
    print("3. Document section recognition (header, case_info, decision, footer)")
    print("4. Comprehensive JSON logging with quality metrics")
    print("5. Chunk-wise analysis and recommendations")
    print("\n📁 Check logs/chunking/ directory for detailed JSON reports")
