"""
Simple test to chunk article.txt and save results to JSON
"""

import json
import datetime
from pathlib import Path
from llama_index.core.schema import TextNode
from app.core.chunking.structure_aware_chunker import create_structure_aware_chunker


def test_article_chunking():
    """Test chunking on article.txt and save to JSON"""
    
    print("🧪 TESTING ARTICLE.TXT CHUNKING")
    print("=" * 50)
    
    # Read article.txt
    article_path = "app/core/chunking/article.txt"
    if not Path(article_path).exists():
        print(f"❌ Article file not found: {article_path}")
        return
    
    with open(article_path, 'r', encoding='utf-8') as f:
        article_text = f.read()
    
    print(f"📄 Article loaded: {len(article_text)} characters")
    lines_count = len(article_text.split('\n'))
    print(f"📄 Lines: {lines_count} lines")
    
    # Create chunker
    chunker = create_structure_aware_chunker(
        max_tokens=600,
        min_chunk_size=100,
        preserve_structure=True
    )
    
    # Create TextNode
    text_node = TextNode(
        text=article_text,
        metadata={
            "source": "article.txt",
            "document_type": "legal_decision",
            "test_date": datetime.datetime.now().isoformat()
        }
    )
    
    # Process chunking
    print("🔄 Processing chunks...")
    chunks, stats = chunker(text_node)
    
    print(f"✅ Chunking complete!")
    print(f"   Sections found: {stats.get('sections_found', 0)}")
    print(f"   Chunks created: {stats.get('chunks_created', 0)}")
    print(f"   Section types: {stats.get('section_types', [])}")
    
    # Prepare JSON output
    timestamp = datetime.datetime.now().isoformat()
    
    # Extract chunk data
    chunk_data = []
    for i, chunk in enumerate(chunks):
        chunk_text = chunk.node.text
        chunk_metadata = chunk.node.metadata or {}
        
        # Analyze chunk
        lines = [line.strip() for line in chunk_text.split('\n') if line.strip()]
        ends_semantically = chunk_text.rstrip().endswith(('।', '?', '!'))
        
        chunk_info = {
            "chunk_id": i + 1,
            "section_type": chunk_metadata.get('section_type', 'unknown'),
            "section_title": chunk_metadata.get('section_title', ''),
            "content": {
                "text": chunk_text,
                "length": len(chunk_text),
                "lines": len(lines),
                "words": len(chunk_text.split()),
                "first_line": lines[0] if lines else "",
                "last_line": lines[-1] if lines else ""
            },
            "quality": {
                "ends_semantically": ends_semantically,
                "has_content": len([w for w in chunk_text.split() if len(w) > 3]) >= 5,
                "length_appropriate": 100 <= len(chunk_text) <= 800
            }
        }
        
        chunk_data.append(chunk_info)
    
    # Create JSON structure
    json_output = {
        "test_info": {
            "timestamp": timestamp,
            "source_file": "article.txt",
            "chunking_method": "structure_aware",
            "chunker_config": {
                "max_tokens": 600,
                "min_chunk_size": 100,
                "preserve_structure": True
            }
        },
        "document_info": {
            "original_length": len(article_text),
            "original_lines": len(article_text.split('\n')),
            "original_words": len(article_text.split()),
            "preview": article_text[:200] + "..." if len(article_text) > 200 else article_text
        },
        "chunking_results": {
            "stats": stats,
            "total_chunks": len(chunks),
            "chunks": chunk_data
        },
        "analysis": {
            "avg_chunk_length": sum(len(chunk.node.text) for chunk in chunks) / len(chunks) if chunks else 0,
            "semantic_endings": sum(1 for chunk in chunks if chunk.node.text.rstrip().endswith(('।', '?', '!'))),
            "semantic_endings_pct": round(sum(1 for chunk in chunks if chunk.node.text.rstrip().endswith(('।', '?', '!'))) / len(chunks) * 100, 1) if chunks else 0,
            "section_distribution": {}
        }
    }
    
    # Calculate section distribution
    section_counts = {}
    for chunk in chunks:
        section_type = chunk.node.metadata.get('section_type', 'unknown')
        section_counts[section_type] = section_counts.get(section_type, 0) + 1
    json_output["analysis"]["section_distribution"] = section_counts
    
    # Save to JSON file
    output_file = f"article_chunking_test_{timestamp.replace(':', '-').replace('.', '-')}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_output, f, ensure_ascii=False, indent=2)
    
    print(f"💾 JSON saved to: {output_file}")
    
    # Print summary
    print(f"\n📊 CHUNKING SUMMARY:")
    print(f"   Total chunks: {len(chunks)}")
    print(f"   Avg chunk length: {json_output['analysis']['avg_chunk_length']:.0f} chars")
    print(f"   Semantic endings: {json_output['analysis']['semantic_endings']}/{len(chunks)} ({json_output['analysis']['semantic_endings_pct']:.1f}%)")
    print(f"   Section types: {list(section_counts.keys())}")
    
    # Show chunk previews
    print(f"\n📝 CHUNK PREVIEWS:")
    for i, chunk_info in enumerate(chunk_data[:5]):  # Show first 5
        section_type = chunk_info['section_type']
        preview = chunk_info['content']['text'][:80].replace('\n', ' ')
        length = chunk_info['content']['length']
        quality = "✅" if chunk_info['quality']['ends_semantically'] else "❌"

        print(f"   {i+1}. [{section_type}] ({length} chars) {quality} {preview}...")
    
    if len(chunk_data) > 5:
        print(f"   ... and {len(chunk_data) - 5} more chunks")
    
    return output_file, json_output


if __name__ == "__main__":
    try:
        output_file, results = test_article_chunking()
        print(f"\n✅ Test completed successfully!")
        print(f"📁 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
