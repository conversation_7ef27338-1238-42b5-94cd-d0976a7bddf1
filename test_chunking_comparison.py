"""
Test script to compare old vs new chunking approaches for Nepali legal documents
"""

import asyncio
import os
from pymongo import AsyncMongoClient
from llama_index.core.schema import TextNode
from improved_nepali_chunker import ImprovedNepaliSentenceChunkTransform
import re


class OldNepaliSentenceChunkTransform:
    """Original simple chunker for comparison"""
    
    def __init__(self, max_tokens=500, overlap=0):
        self.max_tokens = max_tokens
        self.overlap = overlap
    
    def split_sentences(self, text: str) -> list[str]:
        return re.split(r'(?<=[।!?])\s+', text)
    
    def transform_single_text_node(self, node: TextNode):
        sentences = self.split_sentences(node.text)
        total_sentences = len(sentences)
        total_tokens = len(node.text)

        new_nodes = []
        chunk = []
        token_count = 0

        for i, sentence in enumerate(sentences):
            token_count += len(sentence)
            chunk.append(sentence)

            if token_count >= self.max_tokens:
                combined = ' '.join(chunk)
                new_nodes.append(TextNode(text=combined))

                if self.overlap > 0:
                    chunk = chunk[-self.overlap:]
                    token_count = sum(len(s) for s in chunk)
                else:
                    chunk = []
                    token_count = 0

        if chunk:
            combined = ' '.join(chunk)
            new_nodes.append(TextNode(text=combined))

        stats = {
            "total_tokens": total_tokens,
            "total_sentences": total_sentences,
            "chunks_created": len(new_nodes),
            "avg_sentences_per_chunk": round(total_sentences / len(new_nodes), 2) if new_nodes else 0
        }

        return new_nodes, stats


async def test_chunking_comparison():
    """Compare old vs new chunking on real database documents"""
    
    # Connect to database
    uri ="mongodb://172.16.16.54:8300/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.2.10"
    if not uri:
        print("MONGO_URI environment variable not set")
        return
    
    client = AsyncMongoClient(uri)
    db = client["legal_backend_db"]
    collection = db["documents"]
    
    # Get a few sample documents
    docs = await collection.find().limit(3).to_list(length=3)
    
    if not docs:
        print("No documents found in database")
        return
    
    # Initialize chunkers
    old_chunker = OldNepaliSentenceChunkTransform(max_tokens=500, overlap=2)
    new_chunker = ImprovedNepaliSentenceChunkTransform(max_tokens=500, overlap=2)
    
    print("=" * 80)
    print("CHUNKING COMPARISON RESULTS")
    print("=" * 80)
    
    for i, doc in enumerate(docs):
        print(f"\n📄 DOCUMENT {i+1}")
        print(f"Title: {doc.get('metadata', {}).get('parties', {}).get('plaintiff', 'Unknown')[:100]}...")
        print(f"Original text length: {len(doc['text'])} characters")
        print(f"Court type: {doc.get('metadata', {}).get('court_type', 'Unknown')}")
        
        # Create test node
        test_node = TextNode(text=doc["text"], metadata=doc["metadata"])
        
        # Test old chunker
        print(f"\n🔴 OLD CHUNKER RESULTS:")
        try:
            old_chunks, old_stats = old_chunker.transform_single_text_node(test_node)
            print(f"  Chunks created: {old_stats['chunks_created']}")
            print(f"  Total sentences: {old_stats['total_sentences']}")
            print(f"  Avg sentences per chunk: {old_stats['avg_sentences_per_chunk']}")
            
            # Show first chunk sample
            if old_chunks:
                first_chunk = old_chunks[0].text[:200] + "..." if len(old_chunks[0].text) > 200 else old_chunks[0].text
                print(f"  First chunk sample: {first_chunk}")
        except Exception as e:
            print(f"  Error: {e}")
        
        # Test new chunker
        print(f"\n🟢 NEW CHUNKER RESULTS:")
        try:
            new_chunks, new_stats = new_chunker.transform_single_text_node(test_node)
            print(f"  Chunks created: {new_stats['chunks_created']}")
            print(f"  Total sentences: {new_stats['total_sentences']}")
            print(f"  Avg sentences per chunk: {new_stats['avg_sentences_per_chunk']}")
            print(f"  Avg tokens per chunk: {new_stats['avg_tokens_per_chunk']}")
            
            # Show first chunk sample
            if new_chunks:
                first_chunk = new_chunks[0].node.text[:200] + "..." if len(new_chunks[0].node.text) > 200 else new_chunks[0].node.text
                print(f"  First chunk sample: {first_chunk}")
                print(f"  Chunk metadata: {new_chunks[0].node.metadata}")
        except Exception as e:
            print(f"  Error: {e}")
        
        # Compare sentence splitting quality
        print(f"\n🔍 SENTENCE SPLITTING COMPARISON:")
        
        # Old approach
        old_sentences = old_chunker.split_sentences(doc["text"][:1000])  # First 1000 chars
        print(f"  Old approach found {len(old_sentences)} sentences in first 1000 chars")
        if old_sentences:
            print(f"  Sample old sentence: {old_sentences[0][:100]}...")
        
        # New approach  
        new_sentences = new_chunker.split_sentences(doc["text"][:1000])  # First 1000 chars
        print(f"  New approach found {len(new_sentences)} sentences in first 1000 chars")
        if new_sentences:
            print(f"  Sample new sentence: {new_sentences[0][:100]}...")
        
        print("-" * 80)
    
    await client.close()


def test_with_sample_text():
    """Test with a known problematic sample"""
    
    sample_text = """निर्णय नं. १०१६४ - निर्णय दर्ता बदर

भाग: ६१ साल: २०७६ महिना: बैशाख अंक: १

फैसला मिति :२०७४/०९/०३ २३९९

सर्वोच्च अदालत, संयुक्त इजलास

सम्माननीय प्रधानन्यायाधीश श्री गोपाल पराजुली

माननीय न्यायाधीश श्री केदारप्रसाद चालिसे

फैसला मिति : २०७४।९।३

&nbsp;

मुद्दा:- निर्णय दर्ता बदर

&nbsp;

०६७-CI-१५४९

पुनरावेदक / प्रतिवादी : काठमाडौं जिल्ला, कपन गा.वि.स. वडा नं. ३ बस्ने कान्छी तामाङसमेत

विरूद्ध

प्रत्यर्थी / वादी : का.जि.का.म.न.पा. वडा नं. २३ बस्ने नातीभाइ गुभाजु भन्ने नाति शाक्यकी श्रीमती रत्नमाया शाक्य

०६७-CI-१५४८

पुनरावेदक / प्रतिवादी : काठमाडौं जिल्ला, कपन गा.वि.स. वडा नं. ३ बस्ने सन्तबहादुर लामा तामाङसमेत

विरूद्ध

प्रत्यर्थी / वादी : का.जि.का.म.न.पा. वडा नं. २३ बस्ने रत्नमाया शाक्य

&nbsp;

विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने ।

(प्रकरण नं.२)

कुनै मुद्दाको रोहमा प्रमाण बुझ्ने प्रसङ्गमा जारी भएको म्याद पक्षले प्राप्त गर्दैमा सो मुद्दामा पछि हुने निर्णयसमेत निजलाई जानकारी थियो भन्न नमिल्ने ।"""

    print("=" * 80)
    print("SAMPLE TEXT COMPARISON")
    print("=" * 80)
    
    # Test old chunker
    old_chunker = OldNepaliSentenceChunkTransform(max_tokens=300, overlap=1)
    old_sentences = old_chunker.split_sentences(sample_text)
    
    print(f"🔴 OLD CHUNKER:")
    print(f"Found {len(old_sentences)} sentences")
    for i, sentence in enumerate(old_sentences[:5]):  # Show first 5
        print(f"  {i+1}: {sentence[:100]}...")
    
    # Test new chunker
    new_chunker = ImprovedNepaliSentenceChunkTransform(max_tokens=300, overlap=1)
    new_sentences = new_chunker.split_sentences(sample_text)
    
    print(f"\n🟢 NEW CHUNKER:")
    print(f"Found {len(new_sentences)} sentences")
    for i, sentence in enumerate(new_sentences[:5]):  # Show first 5
        print(f"  {i+1}: {sentence[:100]}...")
    
    # Test full chunking
    test_node = TextNode(text=sample_text, metadata={"test": True})
    
    print(f"\n📊 FULL CHUNKING COMPARISON:")
    
    old_chunks, old_stats = old_chunker.transform_single_text_node(test_node)
    print(f"Old chunker: {old_stats}")
    
    new_chunks, new_stats = new_chunker.transform_single_text_node(test_node)
    print(f"New chunker: {new_stats}")


if __name__ == "__main__":
    print("Testing chunking approaches...")
    
    # Test with sample text first
    test_with_sample_text()
    
    # Test with real database if available
    print("\n" + "="*80)
    print("TESTING WITH REAL DATABASE...")
    print("="*80)
    
    try:
        asyncio.run(test_chunking_comparison())
    except Exception as e:
        print(f"Database test failed: {e}")
        print("Make sure MONGO_URI environment variable is set and database is accessible")
