"""
Simple test for structure-aware chunking
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

try:
    from llama_index.core.schema import TextNode
    from app.core.chunking.structure_aware_chunker import create_structure_aware_chunker
    
    print("✅ Imports successful!")
    
    # Test with sample text from article.txt
    sample_text = """निर्णय नं. १०१६४ - निर्णय दर्ता बदर

भाग: ६१ साल: २०७६ महिना: बैशाख अंक: १

फैसला मिति :२०७४/०९/०३ २३९९

सर्वोच्च अदालत, संयुक्त इजलास

&nbsp;

मुद्दा:- निर्णय दर्ता बदर

&nbsp;

०६७-CI-१५४९

पुनरावेदक / प्रतिवादी : काठमाडौं जिल्ला, कपन गा.वि.स. वडा नं. ३ बस्ने कान्छी तामाङसमेत

विरूद्ध

प्रत्यर्थी / वादी : का.जि.का.म.न.पा. वडा नं. २३ बस्ने नातीभाइ गुभाजु भन्ने नाति शाक्यकी श्रीमती रत्नमाया शाक्य

&nbsp;

विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने ।

(प्रकरण नं.२)

कुनै मुद्दाको रोहमा प्रमाण बुझ्ने प्रसङ्गमा जारी भएको म्याद पक्षले प्राप्त गर्दैमा सो मुद्दामा पछि हुने निर्णयसमेत निजलाई जानकारी थियो भन्न नमिल्ने ।

फैसला

प्र.न्या.श्री गोपाल पराजुली : न्याय प्रशासन ऐन, २०४८ को दफा ९ बमोजिम दायर हुन आएको प्रस्तुत मुद्दाको संक्षिप्त तथ्य र ठहर यसप्रकार रहेको छः

&nbsp;

उक्त रायमा सहमत छु ।

न्या. केदारप्रसाद चालिसे

इजलास अधिकृत : हर्कबहादुर क्षेत्री

भर्खरै प्रकाशित नजिरहरू

११२६६ - अंश चलन
फैसला मिति : २०७९/०६/०२ | मुद्दा नं : ०७०-CI-०९८०"""

    print("🧪 Testing structure-aware chunker...")
    
    # Create chunker
    chunker = create_structure_aware_chunker(max_tokens=400, min_chunk_size=50)
    
    # Create test node
    test_node = TextNode(text=sample_text, metadata={"test": True})
    
    # Process
    chunks, stats = chunker(test_node)
    
    print(f"📊 Results:")
    print(f"  Sections found: {stats.get('sections_found', 0)}")
    print(f"  Chunks created: {stats.get('chunks_created', 0)}")
    print(f"  Section types: {stats.get('section_types', [])}")
    
    print(f"\n📝 Chunks:")
    for i, chunk in enumerate(chunks[:3]):  # Show first 3
        metadata = chunk.node.metadata
        text_preview = chunk.node.text[:100].replace('\n', ' ')
        print(f"  {i+1}. {metadata.get('section_type', 'unknown')}: {text_preview}...")
    
    print("\n✅ Test completed successfully!")

except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
